# Milestone Experiment 1: Bidirectional Human-Agent Tech Specs System

## 🎯 Vision

Create a living documentation system that combines **WorkflowMapperAgent** capabilities with bidirectional human-agent collaboration:

1. **Humans write tech specs** → **Agents implement code**
2. **Agents analyze existing code** → **Generate tech specs** → **Humans maintain/refine**
3. **Workflow analysis foundation** → **Semantic code-spec relationships** → **Living documentation**

The system must support both scenarios:
- **Maintenance workflow**: Humans read/write specs, agents execute work based on workflow understanding
- **Bootstrap workflow**: Given blank repo, agents analyze code patterns and generate initial specs for human maintenance

## 🏗 Core Architecture: WorkflowMapperAgent + Bidirectional Enhancement

### Foundation: WorkflowMapperAgent (from ChatGPT conversation)
```yaml
# Core agent capabilities
name: WorkflowMapperAgent
description: |
  Analyzes source-code repositories, constructs rich workflow graphs
  (call + data + I/O), and emits artifacts such as architecture docs,
  OpenAPI/AsyncAPI specs, integration guides, user manuals, and translated code.

modes:
  full_scan: # build baseline from scratch
  incremental_diff: # update existing graph using git-diff

secondary_roles:
  self_audit: # produce confidence scores + "unknown edges" list
```

### Enhancement: Bidirectional Knowledge Bridge
```yaml
# Extended semantic graph schema
entities:
  function:
    properties: [name, signature, file, lang, complexity]
    relationships: [calls, called_by, implements_spec, tested_by]

  milestone:
    properties: [title, status, deadline, dependencies]
    relationships: [implements, blocks, requires, generates_code]

  component:
    properties: [name, type, location, workflow_role]
    relationships: [implements, uses, part_of_workflow]

  workflow_path:
    properties: [entry_point, exit_point, complexity, confidence]
    relationships: [contains_functions, documented_in_spec]

relationships:
  implements:
    from: [function, component, test]
    to: [milestone, requirement, spec]
    bidirectional: true
    metadata: [confidence, last_verified]

  workflow_calls:
    from: [function]
    to: [function]
    metadata: [call_type, frequency, conditions]
```

### Technology Stack (Agent-Maintainable)
- **Core Engine**: Tree-sitter + LangGraph orchestration (from ChatGPT architecture)
- **Storage**: JSON-LD graphs + Git (no databases initially)
- **Parsing**: Multi-language AST + MDX semantic extraction
- **Graph Operations**: DuckDB for queries, Neo4j export capability
- **Processing**: CLI tools + LangGraph agent workflows

## 🔄 Bidirectional Workflows

### Scenario 1: Human → Agent Flow

#### Human Experience
```markdown
<!-- milestone-M0.2.mdx -->
---
title: User Authentication System
type: milestone
priority: high
---

## Requirements
- JWT-based authentication
- Role-based access control
- Password reset functionality

## Components Needed
- AuthService (backend)
- LoginForm (frontend)
- UserModel (database)

## Success Criteria
- [ ] Users can login/logout
- [ ] Admins can manage users
- [ ] Password reset works via email
```

#### Agent Processing Pipeline (Enhanced WorkflowMapperAgent)
```bash
# 1. Spec Ingestion + Workflow Analysis
./workflow-agent.mjs --mode=spec-analysis --input=milestone-M0.2.mdx
# Output: Extracts requirements + identifies workflow implications

# 2. Implementation Planning with Workflow Context
./workflow-agent.mjs --mode=plan --milestone=M0.2 --analyze-workflows
# Output: Task breakdown considering existing code workflows

# 3. Code Generation with Workflow Integration
./workflow-agent.mjs --mode=generate --plan=M0.2-plan.json --preserve-workflows
# Output: Scaffolded components that integrate with existing call graphs

# 4. Bidirectional Linking + Workflow Mapping
./workflow-agent.mjs --mode=link --spec=milestone-M0.2.mdx --code=src/auth/
# Output: Updates graph with implementation + workflow relationships
```

#### Generated Code Structure (Workflow-Aware)
```typescript
// src/auth/AuthService.ts
/**
 * @implements milestone-M0.2#AuthService
 * @spec docs/tech-specs/milestones/milestone-M0.2.mdx
 * @workflow-entry-point login-flow
 * @calls [UserModel.findByEmail, TokenService.generate]
 * @generated-by workflow-mapper-agent-v1.0
 */
export class AuthService {
  // TODO: Implement JWT-based authentication
  // @requirement milestone-M0.2#jwt-auth
  // @workflow-step 1 of login-flow
  async login(credentials: LoginCredentials): Promise<AuthResult> {
    // @workflow-call UserModel.findByEmail
    // @workflow-call TokenService.generate
    throw new Error('Not implemented - see milestone-M0.2.mdx');
  }
}
```

### Scenario 2: Agent → Human Flow (Bootstrap)

#### Bootstrap Command (WorkflowMapperAgent-Powered)
```bash
# Agent analyzes existing codebase with workflow understanding
./workflow-agent.mjs --mode=bootstrap --repo=. --output=docs/tech-specs/

# What it does:
# 1. Builds complete workflow graph (call + data + I/O flows)
# 2. Identifies workflow patterns and entry points
# 3. Maps components to workflow roles
# 4. Generates workflow-aware tech specs
# 5. Creates bidirectional knowledge graph
# 6. Suggests workflow optimizations and missing documentation
```

#### Generated Tech Spec Example (Workflow-Enhanced)
```markdown
<!-- auto-generated/workflow-architecture-analysis.mdx -->
---
title: Workflow Architecture Analysis
type: analysis
generated: true
confidence: 0.87
workflow_coverage: 0.92
last_updated: 2025-01-26
---

## Discovered Workflow Patterns

### User Authentication Flow
**Entry Points**: `POST /login`, `POST /register`
**Workflow Path**: `AuthController → AuthService → UserModel → TokenService`
**Complexity**: Medium (4 steps, 2 external dependencies)
**Coverage**: 85% (missing error handling paths)

**Current Implementation**:
- ✅ Happy path: login → token generation → response
- ✅ User validation and password verification
- ❌ Rate limiting workflow (security gap)
- ❌ Password reset workflow (incomplete)

**Workflow Dependencies**:
- **Database**: User lookup, session storage
- **External**: Email service (password reset)
- **Internal**: Token generation, validation middleware

## Recommended Workflow Improvements
1. **Complete authentication error handling workflows**
2. **Add monitoring and observability to data pipeline**
3. **Document async processing patterns**
4. **Implement workflow-level testing**

---
*This analysis was generated by WorkflowMapperAgent. Workflow coverage indicates percentage of execution paths documented.*
```

## 🛠 Core Tools (Enhanced WorkflowMapperAgent Architecture)

### 1. Workflow-Aware Spec Parser (`parse-specs.mjs`)
```javascript
// Extracts semantic + workflow information from MDX
const specData = {
  metadata: frontmatter,
  requirements: extractRequirements(content),
  components: extractComponents(content),
  workflows: extractWorkflowReferences(content),
  relationships: extractRelationships(content),
  success_criteria: extractCriteria(content),
  workflow_implications: analyzeWorkflowImpact(content)
};
```

### 2. Multi-Language Code Analyzer (`analyze-code.mjs`)
```javascript
// Uses tree-sitter + workflow analysis (from ChatGPT architecture)
const codeData = {
  functions: extractFunctions(ast),
  call_graph: buildCallGraph(ast),
  data_flow: analyzeDataFlow(ast),
  io_operations: detectIOCalls(ast),
  workflow_paths: identifyWorkflowPaths(call_graph),
  complexity: calculateComplexity(ast),
  spec_references: findSpecReferences(comments)
};
```

### 3. Unified Graph Builder (`build-graph.mjs`)
```javascript
// Merges spec, code, and workflow data into JSON-LD graph
const graph = {
  "@context": "https://schema.org/workflow-graph",
  nodes: [...specNodes, ...codeNodes, ...workflowNodes],
  edges: [...implementsEdges, ...callsEdges, ...workflowEdges],
  workflows: [...detectedWorkflows],
  metadata: {
    last_updated,
    confidence_scores,
    workflow_coverage,
    audit_report
  }
};
```

### 4. Bidirectional Workflow Sync (`sync.mjs`)
```javascript
// Keeps specs, code, and workflows in sync
if (codeChanged) {
  updateWorkflowGraph(changedFiles);
  updateSpecStatus(affectedSpecs);
  validateWorkflowIntegrity(affectedWorkflows);
}

if (specChanged) {
  generateWorkflowAwareCode(newRequirements);
  updateImplementationPlan(modifiedSpecs);
  validateWorkflowConsistency(updatedSpecs);
}

if (workflowChanged) {
  updateRelatedSpecs(affectedWorkflows);
  suggestCodeOptimizations(workflowChanges);
}
```

## 📋 Human-Friendly Interfaces

### 1. Web Dashboard
```html
<!-- Simple static site showing current state -->
<div class="milestone-card">
  <h3>Milestone M0.2: Authentication</h3>
  <div class="progress">
    <span class="implemented">3/5 components</span>
    <span class="tested">2/5 tested</span>
  </div>
  <div class="actions">
    <a href="edit-spec.html?id=M0.2">Edit Spec</a>
    <a href="view-code.html?milestone=M0.2">View Code</a>
  </div>
</div>
```

### 2. CLI for Humans
```bash
# Human-friendly commands
techspecs status                    # Show overall progress
techspecs create milestone "Auth"   # Create new milestone
techspecs link component AuthService milestone-M0.2
techspecs validate                  # Check spec-code consistency
```

## 🔄 Bootstrap Process (Blank Repo → Full Specs)

### Phase 1: Discovery
```bash
./bootstrap-specs.mjs --phase=discovery
# Outputs:
# - File structure analysis
# - Dependency graph
# - Identified patterns
# - Suggested component boundaries
```

### Phase 2: Spec Generation
```bash
./bootstrap-specs.mjs --phase=generate
# Outputs:
# - Architecture overview spec
# - Component specifications
# - Process documentation
# - Milestone suggestions
```

### Phase 3: Human Refinement
```bash
./bootstrap-specs.mjs --phase=refine --interactive
# Prompts human for:
# - Spec accuracy validation
# - Missing context
# - Priority adjustments
# - Process preferences
```

### Phase 4: Integration
```bash
./bootstrap-specs.mjs --phase=integrate
# Outputs:
# - Final tech specs
# - Knowledge graph
# - Maintenance workflows
# - Agent configuration
```

## 🎯 Key Design Principles

### 1. Graceful Degradation
- System works even if some components fail
- Humans can always edit specs manually
- Agents provide suggestions, not mandates

### 2. Confidence Scoring
```yaml
# Every generated content has confidence metadata
generated_content:
  confidence: 0.85
  source: "code-analysis"
  human_reviewed: false
  last_validated: "2025-01-26"
```

### 3. Human Override
```markdown
<!-- Humans can always override agent decisions -->
---
agent_generated: true
human_override: true
override_reason: "Agent missed business context"
---
```

### 4. Audit Trail
```json
{
  "changes": [
    {
      "timestamp": "2025-01-26T10:00:00Z",
      "actor": "agent-v1.0",
      "action": "generated_spec",
      "confidence": 0.8
    },
    {
      "timestamp": "2025-01-26T11:00:00Z",
      "actor": "human",
      "action": "refined_requirements",
      "changes": ["added business context", "updated priorities"]
    }
  ]
}
```

## 🚀 Implementation Roadmap (Aligned with ChatGPT Milestones)

### Phase 1: WorkflowMapperAgent Foundation (M0.1-M0.2)
- **M0**: Language detection + repo walker (from ChatGPT)
- **M1**: Tree-sitter parsing → JSON-LD workflow graph
- Enhanced: Bidirectional spec-code linking
- Enhanced: Basic workflow path detection

### Phase 2: Bidirectional Workflows (M0.3-M1.0)
- **M2**: Incremental diff mode + confidence audit (from ChatGPT)
- Enhanced: Real-time spec-code synchronization
- Enhanced: Workflow-aware code generation
- Enhanced: Bootstrap capability for blank repos

### Phase 3: Agent Orchestration (M1.1-M2.0)
- **M3**: OpenAPI/AsyncAPI spec generation (from ChatGPT)
- **M4**: Documentation suite generation
- Enhanced: LangGraph multi-agent orchestration
- Enhanced: Human-agent collaboration interfaces

### Phase 4: Advanced Capabilities (M2.1-M3.0)
- **M5**: Code translation services (from ChatGPT)
- Enhanced: Multi-repo workflow analysis
- Enhanced: Advanced workflow optimization
- Enhanced: Self-improving agent capabilities

## 🎉 Why This Enhanced Approach Works

### Combines Proven Architecture with Innovation
- **WorkflowMapperAgent foundation**: Builds on ChatGPT's proven technical architecture
- **Tree-sitter + LangGraph**: Established tools for parsing and agent orchestration
- **JSON-LD graphs**: Standard format for semantic data representation
- **Bidirectional enhancement**: Adds human-agent collaboration layer

### For One-Person + Agent Teams
- **Agent-maintainable tools**: CLI scripts and configuration files
- **Workflow understanding**: Agents understand code execution paths
- **Incremental processing**: Only analyze changed components
- **Self-auditing**: Confidence scores and coverage metrics

### For Human-Agent Collaboration
- **Workflow-aware generation**: Code generation respects existing execution paths
- **Semantic relationships**: Understand connections between specs and code
- **Bootstrap capability**: Generate initial specs from existing codebases
- **Living documentation**: Specs and code stay synchronized automatically

### For Scalability and Maintenance
- **Multi-language support**: Tree-sitter handles any programming language
- **Graph-based storage**: Efficient querying and relationship tracking
- **Modular architecture**: Replace components without system-wide changes
- **Version control integration**: All state tracked in Git

## 🔬 Next Steps (Updated Strategy)

### Immediate (M0.1): Foundation Validation
1. **Build WorkflowMapperAgent core**: Implement Tree-sitter parsing + basic workflow graph
2. **Test on current repo**: Analyze existing codebase and generate workflow visualization
3. **Validate bidirectional concept**: Ensure spec ↔ code linking works
4. **Create CLI tools**: Basic commands for graph operations

### Short-term (M0.2-M1.0): Bidirectional Workflows
1. **Implement spec parsing**: Extract semantic information from MDX files
2. **Build unified graph**: Merge code and spec data into JSON-LD format
3. **Bootstrap capability**: Generate initial specs from existing repositories
4. **Human interfaces**: Simple web dashboard for graph visualization

### Medium-term (M1.1-M2.0): Agent Orchestration
1. **LangGraph integration**: Multi-agent workflow orchestration
2. **Incremental processing**: Git-diff based updates
3. **Confidence tracking**: Self-audit and coverage metrics
4. **Advanced generation**: Spec generation, code translation, documentation

This enhanced system combines the proven WorkflowMapperAgent architecture with your bidirectional human-agent collaboration vision, creating a foundation that can bootstrap itself and evolve with your development needs.
