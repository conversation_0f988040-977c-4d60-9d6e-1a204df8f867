#!/usr/bin/env bash
set -euo pipefail

echo "🧹 Running M0.2 Acceptance Tests - Agent Dry-Run Cleanup..."

# Test 1: Verify no dry-run references in codebase
echo "1️⃣ Testing for remaining dry-run references..."
DRY_RUN_REFS=$(grep -r "agent:dry-run\|agent-dry-run\|dry-run" . \
    --exclude-dir=node_modules \
    --exclude-dir=.git \
    --exclude-dir=.turbo \
    --exclude="*.log" \
    --exclude="pnpm-lock.yaml" \
    --exclude="m0.2-acceptance.sh" \
    --exclude="milestone-M0.2.mdx" \
    || true)

if [[ -n "$DRY_RUN_REFS" ]]; then
    echo "❌ Found remaining dry-run references:"
    echo "$DRY_RUN_REFS"
    exit 1
fi
echo "✅ No dry-run references found in codebase"

# Test 2: Verify package.json cleanup
echo "2️⃣ Testing package.json cleanup..."
if grep -q "agent:dry-run" code/package.json; then
    echo "❌ agent:dry-run script still exists in code/package.json"
    exit 1
fi
echo "✅ package.json cleaned of dry-run script"

# Test 3: Verify CI workflow cleanup
echo "3️⃣ Testing CI workflow cleanup..."
if grep -q "agent:dry-run" code/.github/workflows/ci.yml; then
    echo "❌ agent:dry-run step still exists in CI workflow"
    exit 1
fi
echo "✅ CI workflow cleaned of dry-run step"

# Test 4: Verify milestone M0 documentation cleanup
echo "4️⃣ Testing milestone M0 documentation cleanup..."
if grep -q "agent:dry-run\|agent-dry-run" docs/tech-specs/milestones/milestone-M0.mdx; then
    echo "❌ dry-run references still exist in milestone M0 documentation"
    exit 1
fi
echo "✅ Milestone M0 documentation cleaned"

# Test 5: Verify spec checklist cleanup
echo "5️⃣ Testing spec checklist cleanup..."
if grep -q "agent:dry-run\|agent-dry-run" docs/tech-specs/spec_checklist.mdx; then
    echo "❌ dry-run references still exist in spec checklist"
    exit 1
fi
echo "✅ Spec checklist cleaned"

# Test 6: Verify work logs updated
echo "6️⃣ Testing work logs accuracy..."
WORK_LOG_FILE="work-log/milestone-M0/implementation-log.md"
if [[ -f "$WORK_LOG_FILE" ]]; then
    if grep -q "Agent Dry-Run Script.*COMPLETED" "$WORK_LOG_FILE"; then
        echo "❌ Work log still claims dry-run script was completed"
        exit 1
    fi
fi
echo "✅ Work logs reflect accurate implementation status"

# Test 7: Verify technical reference cleanup
echo "7️⃣ Testing technical reference cleanup..."
TECH_REF_FILE="work-log/milestone-M0/technical-reference.md"
if [[ -f "$TECH_REF_FILE" ]]; then
    if grep -q "Agent Dry-Run Validation.*scripts/agent-dry-run.mjs" "$TECH_REF_FILE"; then
        echo "❌ Technical reference still documents non-existent dry-run script"
        exit 1
    fi
fi
echo "✅ Technical reference cleaned"

# Test 8: Verify CI pipeline functionality
echo "8️⃣ Testing CI pipeline functionality..."
cd code
echo "   - Testing lint..."
pnpm lint
echo "   - Testing type-check..."
pnpm type-check
echo "   - Testing build..."
pnpm build
echo "   - Testing tests..."
pnpm test --recursive
cd ..
echo "✅ CI pipeline steps work without dry-run"

# Test 9: Verify existing acceptance tests still pass
echo "9️⃣ Testing existing acceptance tests..."
if [[ -f "docs/scripts/acceptance/m0-acceptance.sh" ]]; then
    # Run M0 acceptance but skip the dry-run step if it still exists
    bash docs/scripts/acceptance/m0-acceptance.sh
fi
echo "✅ Existing acceptance tests still pass"

# Test 10: Verify spec-lint still works
echo "🔟 Testing spec-lint functionality..."
node docs/scripts/spec-lint.mjs docs/tech-specs/milestones/milestone-M0.2.mdx
echo "✅ Spec-lint validation passed"

echo "🎉 All M0.2 acceptance tests passed!"
echo "✅ Agent dry-run references have been completely removed"
echo "✅ Repository is clean and CI pipeline is functional"
