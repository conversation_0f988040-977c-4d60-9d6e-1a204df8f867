# Documentation Quick Start

This guide helps you preview and work with MDX specifications in VS Code or Obsidian.

## VS Code Setup

### 1. Install Recommended Extensions

VS Code will automatically suggest the recommended extensions when you open this workspace. Accept the recommendations or install manually:

- **MDX**: `mdx-js.vscode-mdx` - MDX syntax highlighting and preview
- **Markdown Preview Enhanced**: `shd101wyy.markdown-preview-enhanced` - Enhanced markdown preview
- **Markdown Mermaid**: `bierner.markdown-mermaid` - Mermaid diagram support
- **Prettier**: `esbenp.prettier-vscode` - Code formatting

### 2. Preview MDX Files

1. Open any `.mdx` file in `docs/tech-specs/`
2. Press `Ctrl+Shift+V` (Windows/Linux) or `Cmd+Shift+V` (Mac) to open preview
3. Or right-click the file and select "Open Preview"

### 3. Working with Frontmatter

MDX files use YAML frontmatter for metadata:

```yaml
---
title: Your Specification Title
description: Brief description
status: Draft
version: 1.0.0
tags: [milestone, component]
authors: [YourName]
---
```

## Obsidian Setup

### 1. Open as Vault

1. Open Obsidian
2. Click "Open folder as vault"
3. Select the `docs/tech-specs/` directory

### 2. Enable MDX Support

1. Go to Settings → Community Plugins
2. Enable "MDX" plugin if available
3. Or use the built-in Markdown support (works for most content)

### 3. Graph View

Obsidian's graph view will show connections between specifications based on:
- Internal links `[[specification-name]]`
- Tags in frontmatter
- File relationships

## Knowledge Graph

The repository includes a knowledge graph system that parses all MDX specifications:

### Generate Knowledge Graph

```bash
# From the code/ directory
pnpm run build-kg ../docs/tech-specs

# Dry run (no files written)
pnpm run build-kg -- --dry-run ../docs/tech-specs
```

### Output Files

- `kg.jsonld` - JSON-LD format for machine processing
- `kg.yaml` - YAML format for human reading
- `kg-schema.yml` - Schema definition

### Graph Structure

The knowledge graph includes:

- **Entities**: Milestones, Components, Domains, ADRs, Specifications
- **Relationships**: implements, dependsOn, contains, references
- **Properties**: title, description, status, version, tags, authors

## File Organization

```
docs/tech-specs/
├── milestones/          # Project milestones
├── components/          # Component specifications  
├── domains/             # Domain specifications
├── adrs/               # Architectural Decision Records
├── templates/          # Document templates
└── README.md          # This file
```

## Writing Guidelines

### Frontmatter Requirements

All specifications should include:
- `title`: Clear, descriptive title
- `description`: Brief summary
- `status`: Draft, Review, Active, Implemented, Deprecated
- `tags`: Relevant tags for categorization

### Linking Between Specs

Use relative links to reference other specifications:
```markdown
See [Milestone M0.1](./milestones/milestone-M0.1.mdx) for details.
```

### Relationships in Frontmatter

Define relationships between specifications:
```yaml
implements: [target-spec-id]
dependsOn: [dependency-spec-id]
```

## Validation

Validate specifications using the linting tool:
```bash
node docs/scripts/spec-lint.mjs docs/tech-specs/milestones/milestone-M0.1.mdx
```

## Getting Help

- Check existing specifications for examples
- Use the templates in `templates/` directory
- Refer to the knowledge graph schema in `kg-schema.yml`
- Ask team members for guidance on complex specifications
