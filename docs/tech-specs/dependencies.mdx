# Dependencies Documentation

> **📋 Purpose:** Track all project dependencies, their purposes, and maintenance status. Referenced from `[[docs/tech-specs/00_structure.mdx]]`.

---

## 🎯 Dependency Philosophy

- **Minimize dependencies:** Only add dependencies that provide significant value
- **Prefer mature packages:** Choose well-maintained packages with active communities
- **Security first:** Regularly audit and update dependencies for security vulnerabilities
- **License compatibility:** Ensure all dependencies are compatible with project license

---

## 📦 Core Dependencies

### Backend (`apps/api`)

| Package      | Version           | Purpose           | Status      | Notes                          |
| ------------ | ---------------- | ----------------- | ----------- | ------------------------------ |
| `express`    | `^5.0.0-beta.1`  | Web framework     | ✅ Active   | Using beta for latest features |
| `typescript` | `5.4.3`          | Type system       | ✅ Active   | Pinned for stability           |
| `tsup`       | `7.2.0`          | Build tool        | ✅ Active   | Fast ESM builds                |
| `zod`        | `^3.22.0`        | Schema validation | ✅ Active   | Runtime type safety            |
| `jest`       | `29.7.0`         | Testing framework | ✅ Active   | Backend unit tests             |
| `supertest`  | `6.4.2`          | HTTP testing      | ✅ Active   | API endpoint testing           |

### Frontend (`apps/web`)

| Package            | Version      | Purpose         | Status      | Notes                        |
| ------------------ | ----------- | --------------- | ----------- | ---------------------------- |
| `react`            | `^18.2.0`   | UI framework    | ✅ Active   | Latest stable                |
| `react-dom`        | `^18.2.0`   | DOM renderer    | ✅ Active   | Matches React version        |
| `vite`             | `5.2.2`     | Build tool      | ✅ Active   | Fast dev server              |
| `react-router-dom` | `^6.8.0`    | Client routing  | ✅ Active   | Latest v6                    |
| `zustand`          | `^4.4.0`    | State management| ✅ Active   | Lightweight alternative to Redux |
| `react-query`      | `^4.29.0`   | Server state    | ✅ Active   | Data fetching and caching     |
| `vitest`           | `1.5.0`     | Testing framework| ✅ Active   | Fast Vite-native testing      |

### Shared (`packages/shared`)

| Package      | Version     | Purpose           | Status      | Notes                        |
| ------------ | ---------- | ----------------- | ----------- | ---------------------------- |
| `typescript` | `5.4.3`    | Type system       | ✅ Active   | Shared across workspaces     |
| `zod`        | `^3.22.0`  | Schema validation | ✅ Active   | Shared types and validation  |

### Development Tools

| Package        | Version     | Purpose             | Status      | Notes                        |
| -------------- | ---------- | ------------------- | ----------- | ---------------------------- |
| `eslint`       | `8.56.0`   | Linting             | ✅ Active   | Code quality enforcement     |
| `prettier`     | `3.2.5`    | Formatting          | ✅ Active   | Consistent code style        |
| `turbo`        | `latest`   | Monorepo runner     | ✅ Active   | Build orchestration          |
| `husky`        | `^8.0.0`   | Git hooks           | ✅ Active   | Pre-commit validation        |
| `lint-staged`  | `^13.0.0`  | Staged file linting | ✅ Active   | Performance optimization     |

---

## 🔄 Maintenance Schedule

### Weekly
- [ ] Check for security advisories via `npm audit`
- [ ] Review Dependabot PRs

### Monthly
- [ ] Update patch versions: `pnpm update`
- [ ] Review dependency usage and remove unused packages
- [ ] Check for major version updates

### Quarterly
- [ ] Evaluate major version upgrades
- [ ] Review dependency philosophy and choices
- [ ] Update this documentation

---

## 🚨 Security Monitoring

### Automated Tools
- **GitHub Dependabot:** Automatic security updates
- **npm audit:** Regular vulnerability scanning
- **Snyk:** Additional security monitoring (if configured)

### Manual Review Process
1. Review all security advisories weekly
2. Prioritize critical and high severity vulnerabilities
3. Test updates in development before merging
4. Document any breaking changes in `CHANGELOG.md`

---

## 📋 Adding New Dependencies

### Process
1. **Evaluate necessity:** Can we achieve the goal without a new dependency?
2. **Research alternatives:** Compare similar packages for size, performance, maintenance
3. **Check license:** Ensure compatibility with project license
4. **Add to appropriate workspace:** Use `pnpm add` in correct directory
5. **Update this document:** Add entry to relevant table above
6. **Document usage:** Add to relevant code documentation

### Evaluation Criteria
- **Bundle size impact:** Especially important for frontend dependencies
- **Maintenance status:** Last updated, issue response time, community size
- **TypeScript support:** Native types or quality @types package
- **Breaking change frequency:** Stable API preferred
- **Security track record:** History of vulnerabilities and response time

---

## 🔗 Useful Commands

```bash
# Check for outdated dependencies
pnpm outdated

# Update all dependencies to latest patch versions
pnpm update

# Add dependency to specific workspace
pnpm --filter apps/api add express

# Remove unused dependencies
pnpm prune

# Security audit
pnpm audit

# Generate dependency tree
pnpm list --depth=2
```

---

## 📚 References

- [pnpm Workspace Documentation](https://pnpm.io/workspaces)
- [npm Security Best Practices](https://docs.npmjs.com/security)
- [Dependency Management Best Practices](https://blog.bitsrc.io/npm-dependencies-management-best-practices-b1c3b0b5b0e5)
