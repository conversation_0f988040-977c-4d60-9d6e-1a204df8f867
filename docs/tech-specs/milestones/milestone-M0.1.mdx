---
title: "Milestone M0.1 — Knowledge-Graph Bootstrap"
description: "Parse existing MDX specs into a JSON-LD + YAML graph; emit CLI tools every agent can run"
status: "Implemented"
version: "1.0.0"
created: "2025-01-25"
updated: "2025-01-25"
tags: ["milestone", "knowledge-graph", "cli", "parsing"]
authors: ["Augment Agent"]
---

# Milestone M0.1 — Knowledge-Graph Bootstrap

## Overview

Parse existing MDX specification files into a structured knowledge graph with JSON-LD and YAML outputs. Provide CLI tools that can be used by any agent to build and validate the knowledge graph.

## Success Criteria

- **SC-1** `pnpm run build-kg -- --dry-run docs/tech-specs` exits 0
- **SC-2** Running without `--dry-run` writes both graph files (`kg.jsonld` and `kg.yaml`)
- **SC-3** CI `graph.yml` job passes on PR & push
- **SC-4** `kg.yaml` shows at least: one milestone node (M0), one component node, one implements edge
- **SC-5** Spec passes checklist lint: `node docs/scripts/spec-lint.mjs docs/tech-specs/milestones/milestone-M0.1.mdx`

## Deliverables

### Core Packages
- `code/packages/spec-parser-lib/` - TypeScript library for parsing MDX specifications
- `code/packages/kg-cli/` - CLI tool for building knowledge graphs

### Schema & Output
- `kg-schema.yml` - YAML schema defining entities, relationships, and validation rules
- `kg.jsonld` - JSON-LD knowledge graph output
- `kg.yaml` - YAML knowledge graph output

### Infrastructure
- `.github/workflows/graph.yml` - CI workflow for graph validation
- `pnpm run build-kg` script in root package.json

### Documentation & Tooling
- `.vscode/extensions.json` - VS Code extensions for MDX support
- `docs/README.md` - Quick-start guide for VS Code and Obsidian

## Technical Requirements

### Dependencies
- `gray-matter: "4.0.3"` for front-matter parsing
- `yaml: "2.3.2"` for YAML output
- `uuid: "9.0.0"` for deterministic IDs
- TypeScript 5.4.3 compatibility
- Node 20.11.0 compatibility
- pnpm 8.15.4 compatibility

### Package Structure
- Follow existing `@workflow-mapper/package-name` convention
- Use tsup for building with ESM format and .d.ts generation
- Use Jest for testing with 70%+ coverage thresholds
- Include TypeScript, build, test, and type-check scripts

### Knowledge Graph Features
- Parse MDX front-matter and content
- Extract headings and structure
- Detect entity types: Milestone, Component, Domain, ADR, Specification
- Extract relationships: implements, dependsOn, contains, references
- Generate deterministic IDs for entities
- Support both JSON-LD and YAML output formats

## Implementation Plan

### Phase 1: Package Scaffolding
1. Create spec-parser-lib package with TypeScript setup
2. Create kg-cli package with CLI configuration
3. Set up build and test infrastructure

### Phase 2: Core Implementation
1. Implement MDX front-matter parsing
2. Implement heading extraction
3. Create knowledge graph schema
4. Implement CLI with dry-run support

### Phase 3: Testing & Validation
1. Write comprehensive tests for both packages
2. Validate against success criteria
3. Set up CI workflow

### Phase 4: Documentation & Integration
1. Create VS Code extensions configuration
2. Write documentation and quick-start guide
3. Final validation and testing

## Validation

### Acceptance Tests
```bash
# Test dry-run functionality
pnpm run build-kg -- --dry-run docs/tech-specs

# Test full graph generation
pnpm run build-kg docs/tech-specs

# Verify CI workflow
# (Runs automatically on PR/push)

# Test specification linting
node docs/scripts/spec-lint.mjs docs/tech-specs/milestones/milestone-M0.1.mdx
```

### Expected Outputs
- `kg.jsonld`: JSON-LD format with proper @context and @graph structure
- `kg.yaml`: Human-readable YAML with entities and relationships
- Console output showing parsed specs count, milestones, components, relationships

## Notes

This milestone establishes the foundation for knowledge graph generation from MDX specifications. Future milestones can build upon this infrastructure to add more sophisticated relationship detection, validation rules, and visualization capabilities.

## Status

**Status**: Implemented  
**Completion Date**: 2025-01-25  
**Implementation**: Successfully completed all success criteria and deliverables
