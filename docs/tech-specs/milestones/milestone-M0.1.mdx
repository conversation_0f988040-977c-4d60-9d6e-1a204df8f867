---
title: Milestone M0.1 — Knowledge-Graph Bootstrap
description: Parse existing MDX specs into a JSON-LD + YAML graph; emit CLI tools every agent can run.
created: 2025-01-25
version: 0.2.0
status: Draft
tags: [milestone]
authors: [WorkflowMapper Team]
---

import { Callout } from '@/components/Callout'

<Callout emoji="🔗">
<strong>Goal:</strong> Produce a machine-readable knowledge graph linking specs → milestones → components.<br/>
No web viewer yet; humans use VS Code / Obsidian.
</Callout>

---

## 🧳 Toolchain Versions

```yaml
node: "20.11.0"
pnpm: "8.15.4"
typescript: "5.4.3"
gray-matter: "4.1.0"        # front-matter parsing
yaml: "2.3.2"               # YAML emit
uuid: "9.0.0"               # deterministic IDs
```

---

## 🎯 Definition of Done

1. `pnpm run build-kg` scans all `docs/tech-specs/**/*.mdx` and writes `kg.jsonld` and `kg.yaml` in repo root.
2. CLI supports `--dry-run` (prints summary, no file write).
3. CI job `graph-build` passes on push & PR.
4. Every MDX spec contains a resolvable `@id` in the graph.
5. Spec passes `spec-lint` and dry-run gates.

---

## 📦 Deliverables

| Path/Artefact | Must contain / do |
|---------------|-------------------|
| `code/packages/spec-parser-lib/` | `parse-specs.ts`, tests, `package.json` |
| `code/packages/kg-cli/` | `build-kg.ts` (CLI), tests |
| `kg-schema.yml` | YAML schema for entities & relationships (adapted from colleague) |
| `kg.jsonld` & `kg.yaml` | Graph outputs (written by CLI) |
| `.github/workflows/graph.yml` | CI step: `pnpm run build-kg -- --dry-run docs/tech-specs` |
| `.vscode/extensions.json` | Recommends MDX + Markdown preview extensions |
| `docs/README.md` | Quick-start: how to preview MDX in VS Code or Obsidian |

---

## 🗂 Directory Layout (after M0.1)

```text
repo-root/
├─ docs/tech-specs/            # author-written specs
├─ kg.jsonld                   # ← generated
├─ kg.yaml                     # ← generated
├─ code/
│  ├─ apps/
│  │   ├─ api/
│  │   ├─ web/
│  │   └─ docs-site/           # will arrive in M0.2
│  └─ packages/
│     ├─ shared/
│     ├─ spec-parser-lib/      # NEW
│     └─ kg-cli/               # NEW
└─ .github/workflows/graph.yml
```

`pnpm-workspace.yaml` globs already include `code/packages/*`.

---

## 🧠 Key Decisions

| Topic | Decision | Rationale |
|-------|----------|-----------|
| Graph format | Dual: JSON-LD (for future Neo4j) + YAML (human) | Both serialise easily; YAML friendlier in PR diffs. |
| Graph schema | Friend's enhanced schema — milestones, components, relationships, confidence. | Aligns with bidirectional vision. |
| Storage location | Committed at repo root (`kg.*`) — versioned in Git. | Full audit trail, branch diffs. |
| No UI in M0.1 | Rely on VS Code / Obsidian; UI deferred to M0.2. | Focus effort on graph, not viewer. |

---

## ✅ Success Criteria

- [ ] **SC-1** `pnpm run build-kg -- --dry-run docs/tech-specs` exits 0.
- [ ] **SC-2** Running without `--dry-run` writes both graph files.
- [ ] **SC-3** CI `graph.yml` job passes on PR & push.
- [ ] **SC-4** `kg.yaml` shows at least: one milestone node (M0), one component node, one implements edge.
- [ ] **SC-5** Spec passes checklist lint:
  ```bash
  node scripts/spec-lint.mjs docs/tech-specs/milestones/milestone-M0.1.mdx
  ```
- [ ] **SC-6** Agent dry-run passes:
  ```bash
  pnpm run agent:dry-run --spec docs/tech-specs/milestones/milestone-M0.1.mdx
  ```

---

## 🔨 Task Breakdown

| # | Branch name | Task | Owner |
|---|-------------|------|-------|
| 01 | `m0.1/parser-lib` | Scaffold spec-parser-lib with TypeScript setup | BE |
| 02 | `m0.1/parse-frontmatter` | Implement MDX front-matter + heading extraction | BE |
| 03 | `m0.1/kg-schema` | Commit kg-schema.yml (entities, relations) | PM |
| 04 | `m0.1/kg-cli` | CLI build-kg.ts (reads specs, writes graphs) | BE |
| 05 | `m0.1/tests` | Jest tests for parser & CLI | BE |
| 06 | `m0.1/ci-graph` | Add .github/workflows/graph.yml | DevOps |
| 07 | `m0.1/editor-support` | .vscode/extensions.json + Obsidian README | FE |
| 08 | `m0.1/spec-quality` | Run spec-lint; mark spec Approved | PM |
| 09 | `m0.1/final-tag` | Merge & tag kg-bootstrap-v0.1.0 | Lead |

<Callout emoji="🗂"> One PR per row; reviewers tick acceptance hints in PR body. </Callout>

---

## 🤖 CI Pipeline (graph.yml)

```yaml
name: Graph Build
on:
  push:
    branches: [main]
  pull_request:

jobs:
  build-graph:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v2
        with: { version: 8.15.4 }
      - run: corepack enable && pnpm install
      - run: pnpm run build-kg -- --dry-run docs/tech-specs
```

(Full build without `--dry-run` can run only on main if you prefer.)

---

## 🧪 Acceptance Tests

### 1️⃣ Dry-run check
```bash
pnpm run build-kg -- --dry-run docs/tech-specs
# Expect: prints summary, exits 0, no kg.* files written
```

### 2️⃣ Full graph build
```bash
pnpm run build-kg
ls kg.jsonld kg.yaml   # files exist
yq '.entities.milestone' kg.yaml   # at least one node
```

### 3️⃣ CI green
CI job `Graph Build` must pass on PR → merge.

### 4️⃣ Editor preview
Open `docs/tech-specs/milestones/milestone-M0.mdx` in VS Code, toggle Markdown Preview — confirm headings & front-matter visible.

When all criteria are green, merge to main, tag `kg-bootstrap-v0.1.0`, and open Milestone M0.2.
