---
title: Repository Structure & Conventions
description: Living guideline—update with every structural PR. Single source of truth for project structure.
created: 2025-05-25
updated: 2025-05-25
version: 0.2.0
status: Living
tags: [structure]
authors: [nitishMehrotra]
---

> **📋 Single Source of Truth:** This document is the authoritative reference for all project structure, conventions, and governance. The `.cursor/rules/root-rule.mdc` file references this document for consistency.

---

## 🏗️ Repo Philosophy

- **Graph‑first backend** – The JSON‑LD graph is the canonical state; every API mutates or queries it—no parallel domain models.
- **Graph schema versioning & observability** – All changes to the graph schema must be versioned and observable for stability and traceability.
- **Dual-mode scan** – The CLI supports both full and incremental scans; contributors should understand why two CLI targets exist (full for cold start, incremental for fast dev/test cycles).
- **Type‑safe monorepo** – Two workspaces (`apps/api`, `apps/web`) in strict TypeScript, sharing types in `packages/shared`.
- **Incremental & observable** – Prefer additive PRs and surface coverage / audit metrics; avoid mass renames.
- **Human‑review friendly** – Emit TODOs or request clarification instead of guessing.

---

## 📁 Top-Level Folders

| Folder                | Purpose                                         |
|----------------------|-------------------------------------------------|
| `apps/`              | Deployable applications (`api`, `web`, `docs-site`). |
| `packages/`          | Re-usable libs: `shared`, `parser-lib`, `agent-core`, … |
| `docs/tech-specs/`   | Milestone & domain specs (this folder).         |
| `.github/`           | Workflow files, CODEOWNERS.                     |
| `docker-compose.yml` | Local dev stack (api + neo4j).                  |

### Detailed Directory Structure

```text
/
├─ apps/
│  ├─ api/                # Express + ts‑node backend
│  │  ├─ src/
│  │  │   ├─ agent/       # graph builder logic & tools
│  │  │   ├─ api/         # REST controllers & routes
│  │  │   └─ index.ts     # app bootstrap
│  │  ├─ tests/           # jest unit & integration tests
│  │  └─ tsconfig.json
│  │
│  ├─ web/                # React 18 + Vite SPA
│  │  ├─ src/
│  │  │   ├─ components/
│  │  │   ├─ pages/
│  │  │   └─ hooks/
│  │  ├─ tests/           # vitest + RTL tests
│  │  └─ tsconfig.json
│  │
│  └─ docs-site/          # Docusaurus 2 documentation site
│     ├─ src/components/  # Callout and other doc components
│     ├─ docusaurus.config.js  # configured to read ../../docs/tech-specs/
│     ├─ sidebars.js
│     └─ package.json
│
├─ packages/
│  ├─ shared/             # common TypeScript types & utils
│  └─ (future libs)/      # parser-lib, agent-core, UI components
│
├─ docs/
│  ├─ tech-specs/         # Technical specifications (this folder)
│  │  ├── 00_structure.mdx # Single source of truth for project structure
│  │  ├── decision-log.mdx # Architectural Decision Records (ADRs)
│  │  ├── dependencies.md  # Dependency documentation and policies
│  │  ├── spec_checklist.mdx # Spec validation requirements
│  │  ├── milestones/      # Individual milestone specifications
│  │  ├── domains/         # Domain-specific architecture specs
│  │  ├── adrs/            # Architectural Decision Records (individual files)
│  │  ├── templates/       # Document templates for consistency
│  │  └── scripts/         # Validation and generation tools
│  ├─ user-guides/        # User-facing documentation (future)
│  └─ project/            # Project management docs (future)
│
│  # Note: docs-site app configured to read these MDX files directly
├─ .cursor/rules/         # Cursor-specific guidance
├─ e2e/                   # end-to-end Playwright tests (future)
├─ package.json           # root (pnpm workspace)
└─ README.md
```

### Documentation Organization Principles

- **Single source of truth**: `00_structure.mdx` is the authoritative reference
- **Template-driven**: Use templates for consistency across documents
- **Validation-first**: All specs must pass linting before approval
- **Separation of concerns**: Technical specs vs user guides vs project docs
- **Tooling support**: Scripts automate common documentation tasks

---

## 🧩 Coding Standards

### General Rules
- **Strict TypeScript:** Use `noImplicitAny`, `noUncheckedIndexedAccess`
- **Named exports only** — avoids default export confusion; **forbid non-deterministic re-exports** (`export *`)
- **Function size:** Functions ≤60 lines; split otherwise
- **Linting:** `eslint --max-warnings 0`; format with Prettier
- **Commit format:** Enforce conventional commit message format
- **Error codes:** Use structured error codes for all thrown errors and API responses
- **Coverage:** Maintain code coverage targets and report coverage in CI
- **Accessibility:** Ensure all UI components meet accessibility standards

### Backend Standards
- **Routes:** Express routes live in `apps/api/src/routes.ts`; controllers in `apps/api/src/controllers/*`
- **Validation:** Use Zod schemas colocated with DTOs
- **Result type:** All route handlers return `Result<T, E>` objects:
  ```ts
  // packages/shared/Result.ts
  export type Result<T, E extends Error> = { ok: true; data: T } | { ok: false; error: E };
  ```
- **Error handling:** Centralised error middleware at `apps/api/src/errorHandler.ts`

### Frontend Standards
- **Components:** Functional React components only; prefer hooks
- **Location:** Components live under `apps/web/src/components` with `.tsx` extension and colocated CSS Modules
- **State management:** Use React Query for async data; Zustand for client state

### Shared Package Standards
- **Exports:** Re‑export cross‑cutting interfaces from `packages/shared/index.ts` to avoid deep imports
- **Types:** Shared discriminated unions (`Result`, `Maybe`) live in `packages/shared`

---

## 🔧 Tech Stack & Build Tools

### Core Technologies
| Layer     | Runtime / Core libs                | Validation / State | Test libs                |
|-----------|------------------------------------|--------------------|--------------------------|
| Backend   | Node 20, Express ^5, tsup (build), node --env-file (runtime) | Zod                | Jest, Supertest          |
| Frontend  | React 18, Vite, React Router v6, Zustand | Zod (client)      | Vitest, React Testing Library |
| Shared    | TypeScript 5.4, eslint + prettier, tsc‑aliases | —                  | —                        |

### Build & Development Tools
| Concern     | Tool                        | Note                              |
|-------------|-----------------------------|-----------------------------------|
| Build libs  | **tsup**                    | ESM outDir `/dist`                |
| Build api   | **tsup**                    | Command in `apps/api/package.json` |
| Build web   | **Vite**                    | `vite build` in `apps/web`        |
| Build docs  | **Docusaurus 2**            | Static site generation for MDX specs |
| Tests       | Jest (api) · Vitest (libs/web) | Fast & familiar                |
| Linting     | ESLint + `@typescript-eslint` | Enforced in CI                  |
| Formatting  | Prettier via ESLint         | Single source of truth            |

### Key Decisions
- **Backend build:** Prefer `tsup` for backend builds to keep production images slim (no TypeScript runtime dependency)
- **RPC option:** Consider adding `@trpc/server` to expose typed procedures if you foresee RPC-style internal calls between UI and API
- **Dependency management:** Regularly review dependencies, use auto-update tooling, and maintain a deprecation/removal policy
- **Documentation:** Add new deps by editing `package.json` and documenting them in `/docs/dependencies.md`

---

## 🧪 Testing Layers

| Layer           | Framework         | Location                    |
|-----------------|------------------|-----------------------------|
| Unit (api)      | Jest + Supertest | `apps/api/src/__tests__`    |
| Unit (libs, web)| Vitest           | Co-located `__tests__`      |
| E2E (future)    | Playwright       | `/e2e` root folder          |

---

## 🎯 Project Milestones

| ID | Summary                  | Success metric                                 |
|----|--------------------------|------------------------------------------------|
| M0 | Monorepo skeleton & CI   | `pnpm test` passes < 60 s; CI pipeline green on main; Docker image builds |
| M1 | Static graph builder     | CLI `pnpm run build-graph` outputs JSON‑LD graph |
| M2 | Incremental diff mode    | Graph update < 1 s for 3‑file change           |
| M3 | Spec generator API       | `GET /api/specs` returns valid OpenAPI YAML    |
| M4 | Docs dashboard UI        | Frontend `/docs` shows graph + docs            |
| M5 | Code translation service | `POST /api/translate` returns compilable Go    |

---

## 🌳 Branching & Release Flow

> **📋 Process Guidelines:** For complete git workflow, branching conventions, commit standards, and release processes, see [Core Process Guidelines](./process/agent-rules/core.mdx#🔄-git-workflow-process).

### Quick Reference
| Branch Type | Convention | Purpose |
|-------------|------------|---------|
| `main` | Always green; squash-and-merge | Production-ready code |
| `milestone/m{X}-{desc}` | Milestone branches | Major milestone work |
| `m{X}/task-{##}-{desc}` | Task branches | Individual milestone tasks |
| `feature/{name}` | Feature branches | Standalone features |
| `hotfix/{desc}` | Hotfix branches | Critical fixes |

### Development Workflow
- Follow branching conventions from process guidelines
- Use conventional commit messages (`type(scope): description`)
- Run quality checks before commits
- Complete acceptance tests before merging

---

## 🛂 Code Ownership

```text
apps/api/**                 @backend-lead
apps/web/**                 @frontend-lead
packages/**                 @backend-lead
docs/tech-specs/**          @engineering-manager
docs/product-requirements/** @product-manager
```

---

## 🛠️ Spec Governance & Tooling

### Document Templates
| Template | Purpose | Usage |
|----------|---------|-------|
| [`templates/milestone-template.mdx`](./templates/milestone-template.mdx) | New milestone specs | `node scripts/generate-milestone.mjs M1 "Title"` |
| [`templates/domain-template.mdx`](./templates/domain-template.mdx) | Domain specifications | Copy and customize manually |
| [`templates/adr-template.mdx`](./templates/adr-template.mdx) | Architectural decisions | `node scripts/generate-adr.mjs 007 "Title"` |

### Process & Quality Guidelines

> **📋 Complete Process Documentation:** For comprehensive quality assurance processes, validation workflows, git procedures, milestone implementation, and all development processes, see [Core Process Guidelines](./process/agent-rules/core.mdx).

### Quick Reference
| Process Area | Primary Documentation | Quick Commands |
|-------------|----------------------|----------------|
| **Quality Assurance** | [Core Process Guidelines](./process/agent-rules/core.mdx#✅-quality-assurance-process) | `node scripts/validate-structure.mjs` |
| **Git Workflow** | [Core Process Guidelines](./process/agent-rules/core.mdx#🔄-git-workflow-process) | `git checkout -b feature/branch-name` |
| **Milestone Process** | [Core Process Guidelines](./process/agent-rules/core.mdx#📋-milestone-implementation-process) | `bash scripts/m{X}-acceptance.sh` |
| **Documentation** | [Core Process Guidelines](./process/agent-rules/core.mdx#📝-documentation-process) | `node scripts/spec-lint.mjs <spec>` |

---

## 📋 General Practices

- **Onboarding:** Maintain up-to-date onboarding documentation for new contributors
- **Security:** Document and enforce security practices for code, dependencies, and infrastructure
- **Code ownership:** Map code ownership for all major modules to ensure accountability and quality
- **Rule changes:** Define a process for proposing and approving rule changes
- **Rule compliance:** Use a rule linter to ensure rule compliance and governance

---

## 🔄 Document Maintenance

🗒️ **Rule Versioning**
When you change any structural rule, increment the version in the front-matter of this file and append a note to `decision-log.mdx`.

**Planning to reorganise folders?** Open a PR that edits this file *first*, then update code. Docs ahead of code = fewer review surprises.

**Cross-reference:** The `.cursor/rules/root-rule.mdc` file references this document as the single source of truth for project structure and conventions.



