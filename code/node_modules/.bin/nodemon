#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/tmp/kloudi-swe-agent/node_modules/.pnpm/nodemon@3.1.0/node_modules/nodemon/bin/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/node_modules/.pnpm/nodemon@3.1.0/node_modules/nodemon/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/node_modules/.pnpm/nodemon@3.1.0/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/node_modules/.pnpm/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/node_modules:/Users/<USER>/tmp/node_modules:/Users/<USER>/node_modules:/Users/<USER>/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/tmp/kloudi-swe-agent/node_modules/.pnpm/nodemon@3.1.0/node_modules/nodemon/bin/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/node_modules/.pnpm/nodemon@3.1.0/node_modules/nodemon/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/node_modules/.pnpm/nodemon@3.1.0/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/node_modules/.pnpm/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/node_modules:/Users/<USER>/tmp/node_modules:/Users/<USER>/node_modules:/Users/<USER>/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../nodemon/bin/nodemon.js" "$@"
else
  exec node  "$basedir/../nodemon/bin/nodemon.js" "$@"
fi
