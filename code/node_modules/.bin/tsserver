#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/tmp/kloudi-swe-agent/node_modules/.pnpm/typescript@5.4.3/node_modules/typescript/bin/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/node_modules/.pnpm/typescript@5.4.3/node_modules/typescript/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/node_modules/.pnpm/typescript@5.4.3/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/node_modules/.pnpm/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/node_modules:/Users/<USER>/tmp/node_modules:/Users/<USER>/node_modules:/Users/<USER>/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/tmp/kloudi-swe-agent/node_modules/.pnpm/typescript@5.4.3/node_modules/typescript/bin/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/node_modules/.pnpm/typescript@5.4.3/node_modules/typescript/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/node_modules/.pnpm/typescript@5.4.3/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/node_modules/.pnpm/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/node_modules:/Users/<USER>/tmp/node_modules:/Users/<USER>/node_modules:/Users/<USER>/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../typescript/bin/tsserver" "$@"
else
  exec node  "$basedir/../typescript/bin/tsserver" "$@"
fi
