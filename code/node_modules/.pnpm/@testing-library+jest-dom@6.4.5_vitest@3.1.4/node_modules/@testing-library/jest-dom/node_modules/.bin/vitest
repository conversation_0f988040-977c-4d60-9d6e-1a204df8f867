#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/vitest@3.1.4_jsdom@24.1.0/node_modules/vitest/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/vitest@3.1.4_jsdom@24.1.0/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/vitest@3.1.4_jsdom@24.1.0/node_modules/vitest/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/vitest@3.1.4_jsdom@24.1.0/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../vitest@3.1.4_jsdom@24.1.0/node_modules/vitest/vitest.mjs" "$@"
else
  exec node  "$basedir/../../../../../../vitest@3.1.4_jsdom@24.1.0/node_modules/vitest/vitest.mjs" "$@"
fi
