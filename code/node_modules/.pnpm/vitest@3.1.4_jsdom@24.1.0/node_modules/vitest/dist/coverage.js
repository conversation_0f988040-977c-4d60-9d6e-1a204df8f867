export { B as BaseCoverageProvider } from './chunks/coverage.D6LCUsnS.js';
import 'node:fs';
import './chunks/_commonjsHelpers.BFTU3MAI.js';
import 'util';
import 'path';
import 'pathe';
import 'tinyrainbow';
import './chunks/defaults.DSxsTG0h.js';
import 'node:os';
import './chunks/env.Dq0hM4Xv.js';
import 'std-env';
import 'node:crypto';
import '@vitest/utils';
import 'node:module';
import 'node:path';
import 'node:process';
import 'node:fs/promises';
import 'node:url';
import 'node:assert';
import 'node:v8';
import 'node:util';
import 'vite';
import './chunks/constants.BZZyIeIE.js';
import 'node:tty';
import 'node:events';
import './chunks/index.CJ0plNrh.js';
import 'tinypool';
import './chunks/typechecker.DYQbn8uK.js';
import 'node:perf_hooks';
import '@vitest/utils/source-map';
import 'tinyexec';
import '@vitest/runner/utils';
import 'fs';
import 'node:worker_threads';
import './path.js';
import 'vite-node/utils';
import './chunks/coverage.0iPg4Wrz.js';
