import { g as globalApis } from './constants.BZZyIeIE.js';
import { V as VitestIndex } from './index.B0uVAVvx.js';
import './vi.ClIskdbk.js';
import '@vitest/expect';
import '@vitest/runner';
import '@vitest/runner/utils';
import 'chai';
import './utils.CgTj3MsC.js';
import '@vitest/utils';
import './_commonjsHelpers.BFTU3MAI.js';
import '@vitest/snapshot';
import '@vitest/utils/error';
import '@vitest/spy';
import '@vitest/utils/source-map';
import './date.CDOsz-HY.js';
import './run-once.Dimr7O9f.js';
import './benchmark.BoF7jW0Q.js';
import 'expect-type';

function registerApiGlobally() {
	globalApis.forEach((api) => {
		globalThis[api] = VitestIndex[api];
	});
}

export { registerApiGlobally };
