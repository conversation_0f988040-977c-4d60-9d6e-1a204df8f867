{"version": 3, "file": "client.mjs", "sources": ["hmr.ts", "overlay.ts", "client.ts"], "sourcesContent": ["import type { Update } from 'types/hmrPayload'\nimport type { ModuleNamespace, ViteHotContext } from 'types/hot'\nimport type { InferCustomEventPayload } from 'types/customEvent'\n\ntype CustomListenersMap = Map<string, ((data: any) => void)[]>\n\ninterface HotModule {\n  id: string\n  callbacks: HotCallback[]\n}\n\ninterface HotCallback {\n  // the dependencies must be fetchable paths\n  deps: string[]\n  fn: (modules: Array<ModuleNamespace | undefined>) => void\n}\n\nexport interface HMRLogger {\n  error(msg: string | Error): void\n  debug(...msg: unknown[]): void\n}\n\nexport interface HMRConnection {\n  /**\n   * Checked before sending messages to the client.\n   */\n  isReady(): boolean\n  /**\n   * Send message to the client.\n   */\n  send(messages: string): void\n}\n\nexport class HMRContext implements ViteHotContext {\n  private newListeners: CustomListenersMap\n\n  constructor(\n    private hmrClient: HMRClient,\n    private ownerPath: string,\n  ) {\n    if (!hmrClient.dataMap.has(ownerPath)) {\n      hmrClient.dataMap.set(ownerPath, {})\n    }\n\n    // when a file is hot updated, a new context is created\n    // clear its stale callbacks\n    const mod = hmrClient.hotModulesMap.get(ownerPath)\n    if (mod) {\n      mod.callbacks = []\n    }\n\n    // clear stale custom event listeners\n    const staleListeners = hmrClient.ctxToListenersMap.get(ownerPath)\n    if (staleListeners) {\n      for (const [event, staleFns] of staleListeners) {\n        const listeners = hmrClient.customListenersMap.get(event)\n        if (listeners) {\n          hmrClient.customListenersMap.set(\n            event,\n            listeners.filter((l) => !staleFns.includes(l)),\n          )\n        }\n      }\n    }\n\n    this.newListeners = new Map()\n    hmrClient.ctxToListenersMap.set(ownerPath, this.newListeners)\n  }\n\n  get data(): any {\n    return this.hmrClient.dataMap.get(this.ownerPath)\n  }\n\n  accept(deps?: any, callback?: any): void {\n    if (typeof deps === 'function' || !deps) {\n      // self-accept: hot.accept(() => {})\n      this.acceptDeps([this.ownerPath], ([mod]) => deps?.(mod))\n    } else if (typeof deps === 'string') {\n      // explicit deps\n      this.acceptDeps([deps], ([mod]) => callback?.(mod))\n    } else if (Array.isArray(deps)) {\n      this.acceptDeps(deps, callback)\n    } else {\n      throw new Error(`invalid hot.accept() usage.`)\n    }\n  }\n\n  // export names (first arg) are irrelevant on the client side, they're\n  // extracted in the server for propagation\n  acceptExports(\n    _: string | readonly string[],\n    callback: (data: any) => void,\n  ): void {\n    this.acceptDeps([this.ownerPath], ([mod]) => callback?.(mod))\n  }\n\n  dispose(cb: (data: any) => void): void {\n    this.hmrClient.disposeMap.set(this.ownerPath, cb)\n  }\n\n  prune(cb: (data: any) => void): void {\n    this.hmrClient.pruneMap.set(this.ownerPath, cb)\n  }\n\n  // Kept for backward compatibility (#11036)\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  decline(): void {}\n\n  invalidate(message: string): void {\n    this.hmrClient.notifyListeners('vite:invalidate', {\n      path: this.ownerPath,\n      message,\n    })\n    this.send('vite:invalidate', { path: this.ownerPath, message })\n    this.hmrClient.logger.debug(\n      `[vite] invalidate ${this.ownerPath}${message ? `: ${message}` : ''}`,\n    )\n  }\n\n  on<T extends string>(\n    event: T,\n    cb: (payload: InferCustomEventPayload<T>) => void,\n  ): void {\n    const addToMap = (map: Map<string, any[]>) => {\n      const existing = map.get(event) || []\n      existing.push(cb)\n      map.set(event, existing)\n    }\n    addToMap(this.hmrClient.customListenersMap)\n    addToMap(this.newListeners)\n  }\n\n  off<T extends string>(\n    event: T,\n    cb: (payload: InferCustomEventPayload<T>) => void,\n  ): void {\n    const removeFromMap = (map: Map<string, any[]>) => {\n      const existing = map.get(event)\n      if (existing === undefined) {\n        return\n      }\n      const pruned = existing.filter((l) => l !== cb)\n      if (pruned.length === 0) {\n        map.delete(event)\n        return\n      }\n      map.set(event, pruned)\n    }\n    removeFromMap(this.hmrClient.customListenersMap)\n    removeFromMap(this.newListeners)\n  }\n\n  send<T extends string>(event: T, data?: InferCustomEventPayload<T>): void {\n    this.hmrClient.messenger.send(\n      JSON.stringify({ type: 'custom', event, data }),\n    )\n  }\n\n  private acceptDeps(\n    deps: string[],\n    callback: HotCallback['fn'] = () => {},\n  ): void {\n    const mod: HotModule = this.hmrClient.hotModulesMap.get(this.ownerPath) || {\n      id: this.ownerPath,\n      callbacks: [],\n    }\n    mod.callbacks.push({\n      deps,\n      fn: callback,\n    })\n    this.hmrClient.hotModulesMap.set(this.ownerPath, mod)\n  }\n}\n\nclass HMRMessenger {\n  constructor(private connection: HMRConnection) {}\n\n  private queue: string[] = []\n\n  public send(message: string): void {\n    this.queue.push(message)\n    this.flush()\n  }\n\n  public flush(): void {\n    if (this.connection.isReady()) {\n      this.queue.forEach((msg) => this.connection.send(msg))\n      this.queue = []\n    }\n  }\n}\n\nexport class HMRClient {\n  public hotModulesMap = new Map<string, HotModule>()\n  public disposeMap = new Map<string, (data: any) => void | Promise<void>>()\n  public pruneMap = new Map<string, (data: any) => void | Promise<void>>()\n  public dataMap = new Map<string, any>()\n  public customListenersMap: CustomListenersMap = new Map()\n  public ctxToListenersMap = new Map<string, CustomListenersMap>()\n\n  public messenger: HMRMessenger\n\n  constructor(\n    public logger: HMRLogger,\n    connection: HMRConnection,\n    // This allows implementing reloading via different methods depending on the environment\n    private importUpdatedModule: (update: Update) => Promise<ModuleNamespace>,\n  ) {\n    this.messenger = new HMRMessenger(connection)\n  }\n\n  public async notifyListeners<T extends string>(\n    event: T,\n    data: InferCustomEventPayload<T>,\n  ): Promise<void>\n  public async notifyListeners(event: string, data: any): Promise<void> {\n    const cbs = this.customListenersMap.get(event)\n    if (cbs) {\n      await Promise.allSettled(cbs.map((cb) => cb(data)))\n    }\n  }\n\n  public clear(): void {\n    this.hotModulesMap.clear()\n    this.disposeMap.clear()\n    this.pruneMap.clear()\n    this.dataMap.clear()\n    this.customListenersMap.clear()\n    this.ctxToListenersMap.clear()\n  }\n\n  // After an HMR update, some modules are no longer imported on the page\n  // but they may have left behind side effects that need to be cleaned up\n  // (.e.g style injections)\n  public async prunePaths(paths: string[]): Promise<void> {\n    await Promise.all(\n      paths.map((path) => {\n        const disposer = this.disposeMap.get(path)\n        if (disposer) return disposer(this.dataMap.get(path))\n      }),\n    )\n    paths.forEach((path) => {\n      const fn = this.pruneMap.get(path)\n      if (fn) {\n        fn(this.dataMap.get(path))\n      }\n    })\n  }\n\n  protected warnFailedUpdate(err: Error, path: string | string[]): void {\n    if (!err.message.includes('fetch')) {\n      this.logger.error(err)\n    }\n    this.logger.error(\n      `[hmr] Failed to reload ${path}. ` +\n        `This could be due to syntax errors or importing non-existent ` +\n        `modules. (see errors above)`,\n    )\n  }\n\n  private updateQueue: Promise<(() => void) | undefined>[] = []\n  private pendingUpdateQueue = false\n\n  /**\n   * buffer multiple hot updates triggered by the same src change\n   * so that they are invoked in the same order they were sent.\n   * (otherwise the order may be inconsistent because of the http request round trip)\n   */\n  public async queueUpdate(payload: Update): Promise<void> {\n    this.updateQueue.push(this.fetchUpdate(payload))\n    if (!this.pendingUpdateQueue) {\n      this.pendingUpdateQueue = true\n      await Promise.resolve()\n      this.pendingUpdateQueue = false\n      const loading = [...this.updateQueue]\n      this.updateQueue = []\n      ;(await Promise.all(loading)).forEach((fn) => fn && fn())\n    }\n  }\n\n  private async fetchUpdate(update: Update): Promise<(() => void) | undefined> {\n    const { path, acceptedPath } = update\n    const mod = this.hotModulesMap.get(path)\n    if (!mod) {\n      // In a code-splitting project,\n      // it is common that the hot-updating module is not loaded yet.\n      // https://github.com/vitejs/vite/issues/721\n      return\n    }\n\n    let fetchedModule: ModuleNamespace | undefined\n    const isSelfUpdate = path === acceptedPath\n\n    // determine the qualified callbacks before we re-import the modules\n    const qualifiedCallbacks = mod.callbacks.filter(({ deps }) =>\n      deps.includes(acceptedPath),\n    )\n\n    if (isSelfUpdate || qualifiedCallbacks.length > 0) {\n      const disposer = this.disposeMap.get(acceptedPath)\n      if (disposer) await disposer(this.dataMap.get(acceptedPath))\n      try {\n        fetchedModule = await this.importUpdatedModule(update)\n      } catch (e) {\n        this.warnFailedUpdate(e, acceptedPath)\n      }\n    }\n\n    return () => {\n      for (const { deps, fn } of qualifiedCallbacks) {\n        fn(\n          deps.map((dep) => (dep === acceptedPath ? fetchedModule : undefined)),\n        )\n      }\n      const loggedPath = isSelfUpdate ? path : `${acceptedPath} via ${path}`\n      this.logger.debug(`[vite] hot updated: ${loggedPath}`)\n    }\n  }\n}\n", "import type { ErrorPayload } from 'types/hmrPayload'\n\n// injected by the hmr plugin when served\ndeclare const __BASE__: string\ndeclare const __HMR_CONFIG_NAME__: string\n\nconst hmrConfigName = __HMR_CONFIG_NAME__\nconst base = __BASE__ || '/'\n\n// Create an element with provided attributes and optional children\nfunction h(\n  e: string,\n  attrs: Record<string, string> = {},\n  ...children: (string | Node)[]\n) {\n  const elem = document.createElement(e)\n  for (const [k, v] of Object.entries(attrs)) {\n    elem.setAttribute(k, v)\n  }\n  elem.append(...children)\n  return elem\n}\n\n// set :host styles to make playwright detect the element as visible\nconst templateStyle = /*css*/ `\n:host {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: 99999;\n  --monospace: 'SFMono-Regular', Consolas,\n  'Liberation Mono', Menlo, Courier, monospace;\n  --red: #ff5555;\n  --yellow: #e2aa53;\n  --purple: #cfa4ff;\n  --cyan: #2dd9da;\n  --dim: #c9c9c9;\n\n  --window-background: #181818;\n  --window-color: #d8d8d8;\n}\n\n.backdrop {\n  position: fixed;\n  z-index: 99999;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  overflow-y: scroll;\n  margin: 0;\n  background: rgba(0, 0, 0, 0.66);\n}\n\n.window {\n  font-family: var(--monospace);\n  line-height: 1.5;\n  max-width: 80vw;\n  color: var(--window-color);\n  box-sizing: border-box;\n  margin: 30px auto;\n  padding: 2.5vh 4vw;\n  position: relative;\n  background: var(--window-background);\n  border-radius: 6px 6px 8px 8px;\n  box-shadow: 0 19px 38px rgba(0,0,0,0.30), 0 15px 12px rgba(0,0,0,0.22);\n  overflow: hidden;\n  border-top: 8px solid var(--red);\n  direction: ltr;\n  text-align: left;\n}\n\npre {\n  font-family: var(--monospace);\n  font-size: 16px;\n  margin-top: 0;\n  margin-bottom: 1em;\n  overflow-x: scroll;\n  scrollbar-width: none;\n}\n\npre::-webkit-scrollbar {\n  display: none;\n}\n\npre.frame::-webkit-scrollbar {\n  display: block;\n  height: 5px;\n}\n\npre.frame::-webkit-scrollbar-thumb {\n  background: #999;\n  border-radius: 5px;\n}\n\npre.frame {\n  scrollbar-width: thin;\n}\n\n.message {\n  line-height: 1.3;\n  font-weight: 600;\n  white-space: pre-wrap;\n}\n\n.message-body {\n  color: var(--red);\n}\n\n.plugin {\n  color: var(--purple);\n}\n\n.file {\n  color: var(--cyan);\n  margin-bottom: 0;\n  white-space: pre-wrap;\n  word-break: break-all;\n}\n\n.frame {\n  color: var(--yellow);\n}\n\n.stack {\n  font-size: 13px;\n  color: var(--dim);\n}\n\n.tip {\n  font-size: 13px;\n  color: #999;\n  border-top: 1px dotted #999;\n  padding-top: 13px;\n  line-height: 1.8;\n}\n\ncode {\n  font-size: 13px;\n  font-family: var(--monospace);\n  color: var(--yellow);\n}\n\n.file-link {\n  text-decoration: underline;\n  cursor: pointer;\n}\n\nkbd {\n  line-height: 1.5;\n  font-family: ui-monospace, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace;\n  font-size: 0.75rem;\n  font-weight: 700;\n  background-color: rgb(38, 40, 44);\n  color: rgb(166, 167, 171);\n  padding: 0.15rem 0.3rem;\n  border-radius: 0.25rem;\n  border-width: 0.0625rem 0.0625rem 0.1875rem;\n  border-style: solid;\n  border-color: rgb(54, 57, 64);\n  border-image: initial;\n}\n`\n\n// Error Template\nconst template = h(\n  'div',\n  { class: 'backdrop', part: 'backdrop' },\n  h(\n    'div',\n    { class: 'window', part: 'window' },\n    h(\n      'pre',\n      { class: 'message', part: 'message' },\n      h('span', { class: 'plugin', part: 'plugin' }),\n      h('span', { class: 'message-body', part: 'message-body' }),\n    ),\n    h('pre', { class: 'file', part: 'file' }),\n    h('pre', { class: 'frame', part: 'frame' }),\n    h('pre', { class: 'stack', part: 'stack' }),\n    h(\n      'div',\n      { class: 'tip', part: 'tip' },\n      'Click outside, press ',\n      h('kbd', {}, 'Esc'),\n      ' key, or fix the code to dismiss.',\n      h('br'),\n      'You can also disable this overlay by setting ',\n      h('code', { part: 'config-option-name' }, 'server.hmr.overlay'),\n      ' to ',\n      h('code', { part: 'config-option-value' }, 'false'),\n      ' in ',\n      h('code', { part: 'config-file-name' }, hmrConfigName),\n      '.',\n    ),\n  ),\n  h('style', {}, templateStyle),\n)\n\nconst fileRE = /(?:[a-zA-Z]:\\\\|\\/).*?:\\d+:\\d+/g\nconst codeframeRE = /^(?:>?\\s*\\d+\\s+\\|.*|\\s+\\|\\s*\\^.*)\\r?\\n/gm\n\n// Allow `ErrorOverlay` to extend `HTMLElement` even in environments where\n// `HTMLElement` was not originally defined.\nconst { HTMLElement = class {} as typeof globalThis.HTMLElement } = globalThis\nexport class ErrorOverlay extends HTMLElement {\n  root: ShadowRoot\n  closeOnEsc: (e: KeyboardEvent) => void\n\n  constructor(err: ErrorPayload['err'], links = true) {\n    super()\n    this.root = this.attachShadow({ mode: 'open' })\n\n    this.root.appendChild(template)\n\n    codeframeRE.lastIndex = 0\n    const hasFrame = err.frame && codeframeRE.test(err.frame)\n    const message = hasFrame\n      ? err.message.replace(codeframeRE, '')\n      : err.message\n    if (err.plugin) {\n      this.text('.plugin', `[plugin:${err.plugin}] `)\n    }\n    this.text('.message-body', message.trim())\n\n    const [file] = (err.loc?.file || err.id || 'unknown file').split(`?`)\n    if (err.loc) {\n      this.text('.file', `${file}:${err.loc.line}:${err.loc.column}`, links)\n    } else if (err.id) {\n      this.text('.file', file)\n    }\n\n    if (hasFrame) {\n      this.text('.frame', err.frame!.trim())\n    }\n    this.text('.stack', err.stack, links)\n\n    this.root.querySelector('.window')!.addEventListener('click', (e) => {\n      e.stopPropagation()\n    })\n\n    this.addEventListener('click', () => {\n      this.close()\n    })\n\n    this.closeOnEsc = (e: KeyboardEvent) => {\n      if (e.key === 'Escape' || e.code === 'Escape') {\n        this.close()\n      }\n    }\n\n    document.addEventListener('keydown', this.closeOnEsc)\n  }\n\n  text(selector: string, text: string, linkFiles = false): void {\n    const el = this.root.querySelector(selector)!\n    if (!linkFiles) {\n      el.textContent = text\n    } else {\n      let curIndex = 0\n      let match: RegExpExecArray | null\n      fileRE.lastIndex = 0\n      while ((match = fileRE.exec(text))) {\n        const { 0: file, index } = match\n        if (index != null) {\n          const frag = text.slice(curIndex, index)\n          el.appendChild(document.createTextNode(frag))\n          const link = document.createElement('a')\n          link.textContent = file\n          link.className = 'file-link'\n          link.onclick = () => {\n            fetch(\n              new URL(\n                `${base}__open-in-editor?file=${encodeURIComponent(file)}`,\n                import.meta.url,\n              ),\n            )\n          }\n          el.appendChild(link)\n          curIndex += frag.length + file.length\n        }\n      }\n    }\n  }\n  close(): void {\n    this.parentNode?.removeChild(this)\n    document.removeEventListener('keydown', this.closeOnEsc)\n  }\n}\n\nexport const overlayId = 'vite-error-overlay'\nconst { customElements } = globalThis // Ensure `customElements` is defined before the next line.\nif (customElements && !customElements.get(overlayId)) {\n  customElements.define(overlayId, ErrorOverlay)\n}\n", "import type { ErrorPayload, HMRPayload } from 'types/hmrPayload'\nimport type { ViteHotContext } from 'types/hot'\nimport type { InferCustomEventPayload } from 'types/customEvent'\nimport { HMRClient, HMRContext } from '../shared/hmr'\nimport { ErrorOverlay, overlayId } from './overlay'\nimport '@vite/env'\n\n// injected by the hmr plugin when served\ndeclare const __BASE__: string\ndeclare const __SERVER_HOST__: string\ndeclare const __HMR_PROTOCOL__: string | null\ndeclare const __HMR_HOSTNAME__: string | null\ndeclare const __HMR_PORT__: number | null\ndeclare const __HMR_DIRECT_TARGET__: string\ndeclare const __HMR_BASE__: string\ndeclare const __HMR_TIMEOUT__: number\ndeclare const __HMR_ENABLE_OVERLAY__: boolean\n\nconsole.debug('[vite] connecting...')\n\nconst importMetaUrl = new URL(import.meta.url)\n\n// use server configuration, then fallback to inference\nconst serverHost = __SERVER_HOST__\nconst socketProtocol =\n  __HMR_PROTOCOL__ || (importMetaUrl.protocol === 'https:' ? 'wss' : 'ws')\nconst hmrPort = __HMR_PORT__\nconst socketHost = `${__HMR_HOSTNAME__ || importMetaUrl.hostname}:${\n  hmrPort || importMetaUrl.port\n}${__HMR_BASE__}`\nconst directSocketHost = __HMR_DIRECT_TARGET__\nconst base = __BASE__ || '/'\n\nlet socket: WebSocket\ntry {\n  let fallback: (() => void) | undefined\n  // only use fallback when port is inferred to prevent confusion\n  if (!hmrPort) {\n    fallback = () => {\n      // fallback to connecting directly to the hmr server\n      // for servers which does not support proxying websocket\n      socket = setupWebSocket(socketProtocol, directSocketHost, () => {\n        const currentScriptHostURL = new URL(import.meta.url)\n        const currentScriptHost =\n          currentScriptHostURL.host +\n          currentScriptHostURL.pathname.replace(/@vite\\/client$/, '')\n        console.error(\n          '[vite] failed to connect to websocket.\\n' +\n            'your current setup:\\n' +\n            `  (browser) ${currentScriptHost} <--[HTTP]--> ${serverHost} (server)\\n` +\n            `  (browser) ${socketHost} <--[WebSocket (failing)]--> ${directSocketHost} (server)\\n` +\n            'Check out your Vite / network configuration and https://vitejs.dev/config/server-options.html#server-hmr .',\n        )\n      })\n      socket.addEventListener(\n        'open',\n        () => {\n          console.info(\n            '[vite] Direct websocket connection fallback. Check out https://vitejs.dev/config/server-options.html#server-hmr to remove the previous connection error.',\n          )\n        },\n        { once: true },\n      )\n    }\n  }\n\n  socket = setupWebSocket(socketProtocol, socketHost, fallback)\n} catch (error) {\n  console.error(`[vite] failed to connect to websocket (${error}). `)\n}\n\nfunction setupWebSocket(\n  protocol: string,\n  hostAndPath: string,\n  onCloseWithoutOpen?: () => void,\n) {\n  const socket = new WebSocket(`${protocol}://${hostAndPath}`, 'vite-hmr')\n  let isOpened = false\n\n  socket.addEventListener(\n    'open',\n    () => {\n      isOpened = true\n      notifyListeners('vite:ws:connect', { webSocket: socket })\n    },\n    { once: true },\n  )\n\n  // Listen for messages\n  socket.addEventListener('message', async ({ data }) => {\n    handleMessage(JSON.parse(data))\n  })\n\n  // ping server\n  socket.addEventListener('close', async ({ wasClean }) => {\n    if (wasClean) return\n\n    if (!isOpened && onCloseWithoutOpen) {\n      onCloseWithoutOpen()\n      return\n    }\n\n    notifyListeners('vite:ws:disconnect', { webSocket: socket })\n\n    console.log(`[vite] server connection lost. polling for restart...`)\n    await waitForSuccessfulPing(protocol, hostAndPath)\n    location.reload()\n  })\n\n  return socket\n}\n\nfunction cleanUrl(pathname: string): string {\n  const url = new URL(pathname, 'http://vitejs.dev')\n  url.searchParams.delete('direct')\n  return url.pathname + url.search\n}\n\nlet isFirstUpdate = true\nconst outdatedLinkTags = new WeakSet<HTMLLinkElement>()\n\nconst debounceReload = (time: number) => {\n  let timer: ReturnType<typeof setTimeout> | null\n  return () => {\n    if (timer) {\n      clearTimeout(timer)\n      timer = null\n    }\n    timer = setTimeout(() => {\n      location.reload()\n    }, time)\n  }\n}\nconst pageReload = debounceReload(50)\n\nconst hmrClient = new HMRClient(\n  console,\n  {\n    isReady: () => socket && socket.readyState === 1,\n    send: (message) => socket.send(message),\n  },\n  async function importUpdatedModule({\n    acceptedPath,\n    timestamp,\n    explicitImportRequired,\n    isWithinCircularImport,\n  }) {\n    const [acceptedPathWithoutQuery, query] = acceptedPath.split(`?`)\n    const importPromise = import(\n      /* @vite-ignore */\n      base +\n        acceptedPathWithoutQuery.slice(1) +\n        `?${explicitImportRequired ? 'import&' : ''}t=${timestamp}${\n          query ? `&${query}` : ''\n        }`\n    )\n    if (isWithinCircularImport) {\n      importPromise.catch(() => {\n        console.info(\n          `[hmr] ${acceptedPath} failed to apply HMR as it's within a circular import. Reloading page to reset the execution order. ` +\n            `To debug and break the circular import, you can run \\`vite --debug hmr\\` to log the circular dependency path if a file change triggered it.`,\n        )\n        pageReload()\n      })\n    }\n    return await importPromise\n  },\n)\n\nasync function handleMessage(payload: HMRPayload) {\n  switch (payload.type) {\n    case 'connected':\n      console.debug(`[vite] connected.`)\n      hmrClient.messenger.flush()\n      // proxy(nginx, docker) hmr ws maybe caused timeout,\n      // so send ping package let ws keep alive.\n      setInterval(() => {\n        if (socket.readyState === socket.OPEN) {\n          socket.send('{\"type\":\"ping\"}')\n        }\n      }, __HMR_TIMEOUT__)\n      break\n    case 'update':\n      notifyListeners('vite:beforeUpdate', payload)\n      // if this is the first update and there's already an error overlay, it\n      // means the page opened with existing server compile error and the whole\n      // module script failed to load (since one of the nested imports is 500).\n      // in this case a normal update won't work and a full reload is needed.\n      if (isFirstUpdate && hasErrorOverlay()) {\n        window.location.reload()\n        return\n      } else {\n        clearErrorOverlay()\n        isFirstUpdate = false\n      }\n      await Promise.all(\n        payload.updates.map(async (update): Promise<void> => {\n          if (update.type === 'js-update') {\n            return hmrClient.queueUpdate(update)\n          }\n\n          // css-update\n          // this is only sent when a css file referenced with <link> is updated\n          const { path, timestamp } = update\n          const searchUrl = cleanUrl(path)\n          // can't use querySelector with `[href*=]` here since the link may be\n          // using relative paths so we need to use link.href to grab the full\n          // URL for the include check.\n          const el = Array.from(\n            document.querySelectorAll<HTMLLinkElement>('link'),\n          ).find(\n            (e) =>\n              !outdatedLinkTags.has(e) && cleanUrl(e.href).includes(searchUrl),\n          )\n\n          if (!el) {\n            return\n          }\n\n          const newPath = `${base}${searchUrl.slice(1)}${\n            searchUrl.includes('?') ? '&' : '?'\n          }t=${timestamp}`\n\n          // rather than swapping the href on the existing tag, we will\n          // create a new link tag. Once the new stylesheet has loaded we\n          // will remove the existing link tag. This removes a Flash Of\n          // Unstyled Content that can occur when swapping out the tag href\n          // directly, as the new stylesheet has not yet been loaded.\n          return new Promise((resolve) => {\n            const newLinkTag = el.cloneNode() as HTMLLinkElement\n            newLinkTag.href = new URL(newPath, el.href).href\n            const removeOldEl = () => {\n              el.remove()\n              console.debug(`[vite] css hot updated: ${searchUrl}`)\n              resolve()\n            }\n            newLinkTag.addEventListener('load', removeOldEl)\n            newLinkTag.addEventListener('error', removeOldEl)\n            outdatedLinkTags.add(el)\n            el.after(newLinkTag)\n          })\n        }),\n      )\n      notifyListeners('vite:afterUpdate', payload)\n      break\n    case 'custom': {\n      notifyListeners(payload.event, payload.data)\n      break\n    }\n    case 'full-reload':\n      notifyListeners('vite:beforeFullReload', payload)\n      if (payload.path && payload.path.endsWith('.html')) {\n        // if html file is edited, only reload the page if the browser is\n        // currently on that page.\n        const pagePath = decodeURI(location.pathname)\n        const payloadPath = base + payload.path.slice(1)\n        if (\n          pagePath === payloadPath ||\n          payload.path === '/index.html' ||\n          (pagePath.endsWith('/') && pagePath + 'index.html' === payloadPath)\n        ) {\n          pageReload()\n        }\n        return\n      } else {\n        pageReload()\n      }\n      break\n    case 'prune':\n      notifyListeners('vite:beforePrune', payload)\n      await hmrClient.prunePaths(payload.paths)\n      break\n    case 'error': {\n      notifyListeners('vite:error', payload)\n      const err = payload.err\n      if (enableOverlay) {\n        createErrorOverlay(err)\n      } else {\n        console.error(\n          `[vite] Internal Server Error\\n${err.message}\\n${err.stack}`,\n        )\n      }\n      break\n    }\n    default: {\n      const check: never = payload\n      return check\n    }\n  }\n}\n\nfunction notifyListeners<T extends string>(\n  event: T,\n  data: InferCustomEventPayload<T>,\n): void\nfunction notifyListeners(event: string, data: any): void {\n  hmrClient.notifyListeners(event, data)\n}\n\nconst enableOverlay = __HMR_ENABLE_OVERLAY__\n\nfunction createErrorOverlay(err: ErrorPayload['err']) {\n  clearErrorOverlay()\n  document.body.appendChild(new ErrorOverlay(err))\n}\n\nfunction clearErrorOverlay() {\n  document.querySelectorAll<ErrorOverlay>(overlayId).forEach((n) => n.close())\n}\n\nfunction hasErrorOverlay() {\n  return document.querySelectorAll(overlayId).length\n}\n\nasync function waitForSuccessfulPing(\n  socketProtocol: string,\n  hostAndPath: string,\n  ms = 1000,\n) {\n  const pingHostProtocol = socketProtocol === 'wss' ? 'https' : 'http'\n\n  const ping = async () => {\n    // A fetch on a websocket URL will return a successful promise with status 400,\n    // but will reject a networking error.\n    // When running on middleware mode, it returns status 426, and an cors error happens if mode is not no-cors\n    try {\n      await fetch(`${pingHostProtocol}://${hostAndPath}`, {\n        mode: 'no-cors',\n        headers: {\n          // Custom headers won't be included in a request with no-cors so (ab)use one of the\n          // safelisted headers to identify the ping request\n          Accept: 'text/x-vite-ping',\n        },\n      })\n      return true\n    } catch {}\n    return false\n  }\n\n  if (await ping()) {\n    return\n  }\n  await wait(ms)\n\n  // eslint-disable-next-line no-constant-condition\n  while (true) {\n    if (document.visibilityState === 'visible') {\n      if (await ping()) {\n        break\n      }\n      await wait(ms)\n    } else {\n      await waitForWindowShow()\n    }\n  }\n}\n\nfunction wait(ms: number) {\n  return new Promise((resolve) => setTimeout(resolve, ms))\n}\n\nfunction waitForWindowShow() {\n  return new Promise<void>((resolve) => {\n    const onChange = async () => {\n      if (document.visibilityState === 'visible') {\n        resolve()\n        document.removeEventListener('visibilitychange', onChange)\n      }\n    }\n    document.addEventListener('visibilitychange', onChange)\n  })\n}\n\nconst sheetsMap = new Map<string, HTMLStyleElement>()\n\n// collect existing style elements that may have been inserted during SSR\n// to avoid FOUC or duplicate styles\nif ('document' in globalThis) {\n  document\n    .querySelectorAll<HTMLStyleElement>('style[data-vite-dev-id]')\n    .forEach((el) => {\n      sheetsMap.set(el.getAttribute('data-vite-dev-id')!, el)\n    })\n}\n\nconst cspNonce =\n  'document' in globalThis\n    ? document.querySelector<HTMLMetaElement>('meta[property=csp-nonce]')?.nonce\n    : undefined\n\n// all css imports should be inserted at the same position\n// because after build it will be a single css file\nlet lastInsertedStyle: HTMLStyleElement | undefined\n\nexport function updateStyle(id: string, content: string): void {\n  let style = sheetsMap.get(id)\n  if (!style) {\n    style = document.createElement('style')\n    style.setAttribute('type', 'text/css')\n    style.setAttribute('data-vite-dev-id', id)\n    style.textContent = content\n    if (cspNonce) {\n      style.setAttribute('nonce', cspNonce)\n    }\n\n    if (!lastInsertedStyle) {\n      document.head.appendChild(style)\n\n      // reset lastInsertedStyle after async\n      // because dynamically imported css will be splitted into a different file\n      setTimeout(() => {\n        lastInsertedStyle = undefined\n      }, 0)\n    } else {\n      lastInsertedStyle.insertAdjacentElement('afterend', style)\n    }\n    lastInsertedStyle = style\n  } else {\n    style.textContent = content\n  }\n  sheetsMap.set(id, style)\n}\n\nexport function removeStyle(id: string): void {\n  const style = sheetsMap.get(id)\n  if (style) {\n    document.head.removeChild(style)\n    sheetsMap.delete(id)\n  }\n}\n\nexport function createHotContext(ownerPath: string): ViteHotContext {\n  return new HMRContext(hmrClient, ownerPath)\n}\n\n/**\n * urls here are dynamic import() urls that couldn't be statically analyzed\n */\nexport function injectQuery(url: string, queryToInject: string): string {\n  // skip urls that won't be handled by vite\n  if (url[0] !== '.' && url[0] !== '/') {\n    return url\n  }\n\n  // can't use pathname from URL since it may be relative like ../\n  const pathname = url.replace(/[?#].*$/, '')\n  const { search, hash } = new URL(url, 'http://vitejs.dev')\n\n  return `${pathname}?${queryToInject}${search ? `&` + search.slice(1) : ''}${\n    hash || ''\n  }`\n}\n\nexport { ErrorOverlay }\n"], "names": ["base"], "mappings": ";;MAiCa,UAAU,CAAA;IAGrB,WACU,CAAA,SAAoB,EACpB,SAAiB,EAAA;QADjB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAW;QACpB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAQ;QAEzB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;YACrC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC,CAAA;AACrC,SAAA;;;QAID,MAAM,GAAG,GAAG,SAAS,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;AAClD,QAAA,IAAI,GAAG,EAAE;AACP,YAAA,GAAG,CAAC,SAAS,GAAG,EAAE,CAAA;AACnB,SAAA;;QAGD,MAAM,cAAc,GAAG,SAAS,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;AACjE,QAAA,IAAI,cAAc,EAAE;YAClB,KAAK,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,cAAc,EAAE;gBAC9C,MAAM,SAAS,GAAG,SAAS,CAAC,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;AACzD,gBAAA,IAAI,SAAS,EAAE;oBACb,SAAS,CAAC,kBAAkB,CAAC,GAAG,CAC9B,KAAK,EACL,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAC/C,CAAA;AACF,iBAAA;AACF,aAAA;AACF,SAAA;AAED,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,GAAG,EAAE,CAAA;QAC7B,SAAS,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC,CAAA;KAC9D;AAED,IAAA,IAAI,IAAI,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;KAClD;IAED,MAAM,CAAC,IAAU,EAAE,QAAc,EAAA;AAC/B,QAAA,IAAI,OAAO,IAAI,KAAK,UAAU,IAAI,CAAC,IAAI,EAAE;;YAEvC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,IAAI,KAAJ,IAAA,IAAA,IAAI,KAAJ,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,IAAI,CAAG,GAAG,CAAC,CAAC,CAAA;AAC1D,SAAA;AAAM,aAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;;YAEnC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,QAAQ,KAAA,IAAA,IAAR,QAAQ,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAR,QAAQ,CAAG,GAAG,CAAC,CAAC,CAAA;AACpD,SAAA;AAAM,aAAA,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;AAC9B,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;AAChC,SAAA;AAAM,aAAA;AACL,YAAA,MAAM,IAAI,KAAK,CAAC,CAAA,2BAAA,CAA6B,CAAC,CAAA;AAC/C,SAAA;KACF;;;IAID,aAAa,CACX,CAA6B,EAC7B,QAA6B,EAAA;QAE7B,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,QAAQ,KAAR,IAAA,IAAA,QAAQ,KAAR,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,QAAQ,CAAG,GAAG,CAAC,CAAC,CAAA;KAC9D;AAED,IAAA,OAAO,CAAC,EAAuB,EAAA;AAC7B,QAAA,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC,CAAA;KAClD;AAED,IAAA,KAAK,CAAC,EAAuB,EAAA;AAC3B,QAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC,CAAA;KAChD;;;AAID,IAAA,OAAO,MAAW;AAElB,IAAA,UAAU,CAAC,OAAe,EAAA;AACxB,QAAA,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,iBAAiB,EAAE;YAChD,IAAI,EAAE,IAAI,CAAC,SAAS;YACpB,OAAO;AACR,SAAA,CAAC,CAAA;AACF,QAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE,CAAC,CAAA;QAC/D,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CACzB,CAAA,kBAAA,EAAqB,IAAI,CAAC,SAAS,CAAA,EAAG,OAAO,GAAG,CAAA,EAAA,EAAK,OAAO,CAAA,CAAE,GAAG,EAAE,CAAE,CAAA,CACtE,CAAA;KACF;IAED,EAAE,CACA,KAAQ,EACR,EAAiD,EAAA;AAEjD,QAAA,MAAM,QAAQ,GAAG,CAAC,GAAuB,KAAI;YAC3C,MAAM,QAAQ,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,CAAA;AACrC,YAAA,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;AACjB,YAAA,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;AAC1B,SAAC,CAAA;AACD,QAAA,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAA;AAC3C,QAAA,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;KAC5B;IAED,GAAG,CACD,KAAQ,EACR,EAAiD,EAAA;AAEjD,QAAA,MAAM,aAAa,GAAG,CAAC,GAAuB,KAAI;YAChD,MAAM,QAAQ,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;YAC/B,IAAI,QAAQ,KAAK,SAAS,EAAE;gBAC1B,OAAM;AACP,aAAA;AACD,YAAA,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAA;AAC/C,YAAA,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;AACvB,gBAAA,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;gBACjB,OAAM;AACP,aAAA;AACD,YAAA,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;AACxB,SAAC,CAAA;AACD,QAAA,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAA;AAChD,QAAA,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;KACjC;IAED,IAAI,CAAmB,KAAQ,EAAE,IAAiC,EAAA;QAChE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAC3B,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAChD,CAAA;KACF;AAEO,IAAA,UAAU,CAChB,IAAc,EACd,WAA8B,SAAQ,EAAA;AAEtC,QAAA,MAAM,GAAG,GAAc,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI;YACzE,EAAE,EAAE,IAAI,CAAC,SAAS;AAClB,YAAA,SAAS,EAAE,EAAE;SACd,CAAA;AACD,QAAA,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC;YACjB,IAAI;AACJ,YAAA,EAAE,EAAE,QAAQ;AACb,SAAA,CAAC,CAAA;AACF,QAAA,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC,CAAA;KACtD;AACF,CAAA;AAED,MAAM,YAAY,CAAA;AAChB,IAAA,WAAA,CAAoB,UAAyB,EAAA;QAAzB,IAAU,CAAA,UAAA,GAAV,UAAU,CAAe;QAErC,IAAK,CAAA,KAAA,GAAa,EAAE,CAAA;KAFqB;AAI1C,IAAA,IAAI,CAAC,OAAe,EAAA;AACzB,QAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QACxB,IAAI,CAAC,KAAK,EAAE,CAAA;KACb;IAEM,KAAK,GAAA;AACV,QAAA,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE;AAC7B,YAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAA;AACtD,YAAA,IAAI,CAAC,KAAK,GAAG,EAAE,CAAA;AAChB,SAAA;KACF;AACF,CAAA;MAEY,SAAS,CAAA;IAUpB,WACS,CAAA,MAAiB,EACxB,UAAyB;;IAEjB,mBAAiE,EAAA;QAHlE,IAAM,CAAA,MAAA,GAAN,MAAM,CAAW;QAGhB,IAAmB,CAAA,mBAAA,GAAnB,mBAAmB,CAA8C;AAbpE,QAAA,IAAA,CAAA,aAAa,GAAG,IAAI,GAAG,EAAqB,CAAA;AAC5C,QAAA,IAAA,CAAA,UAAU,GAAG,IAAI,GAAG,EAA+C,CAAA;AACnE,QAAA,IAAA,CAAA,QAAQ,GAAG,IAAI,GAAG,EAA+C,CAAA;AACjE,QAAA,IAAA,CAAA,OAAO,GAAG,IAAI,GAAG,EAAe,CAAA;AAChC,QAAA,IAAA,CAAA,kBAAkB,GAAuB,IAAI,GAAG,EAAE,CAAA;AAClD,QAAA,IAAA,CAAA,iBAAiB,GAAG,IAAI,GAAG,EAA8B,CAAA;QA8DxD,IAAW,CAAA,WAAA,GAAwC,EAAE,CAAA;QACrD,IAAkB,CAAA,kBAAA,GAAG,KAAK,CAAA;QArDhC,IAAI,CAAC,SAAS,GAAG,IAAI,YAAY,CAAC,UAAU,CAAC,CAAA;KAC9C;AAMM,IAAA,MAAM,eAAe,CAAC,KAAa,EAAE,IAAS,EAAA;QACnD,MAAM,GAAG,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;AAC9C,QAAA,IAAI,GAAG,EAAE;AACP,YAAA,MAAM,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;AACpD,SAAA;KACF;IAEM,KAAK,GAAA;AACV,QAAA,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAA;AAC1B,QAAA,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAA;AACvB,QAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAA;AACrB,QAAA,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAA;AACpB,QAAA,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAA;AAC/B,QAAA,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAA;KAC/B;;;;IAKM,MAAM,UAAU,CAAC,KAAe,EAAA;QACrC,MAAM,OAAO,CAAC,GAAG,CACf,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,KAAI;YACjB,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;AAC1C,YAAA,IAAI,QAAQ;gBAAE,OAAO,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAA;SACtD,CAAC,CACH,CAAA;AACD,QAAA,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;YACrB,MAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;AAClC,YAAA,IAAI,EAAE,EAAE;gBACN,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAA;AAC3B,aAAA;AACH,SAAC,CAAC,CAAA;KACH;IAES,gBAAgB,CAAC,GAAU,EAAE,IAAuB,EAAA;QAC5D,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;AAClC,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;AACvB,SAAA;AACD,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,CAAA,uBAAA,EAA0B,IAAI,CAAI,EAAA,CAAA;YAChC,CAA+D,6DAAA,CAAA;AAC/D,YAAA,CAAA,2BAAA,CAA6B,CAChC,CAAA;KACF;AAKD;;;;AAIG;IACI,MAAM,WAAW,CAAC,OAAe,EAAA;AACtC,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAA;AAChD,QAAA,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;AAC5B,YAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAA;AAC9B,YAAA,MAAM,OAAO,CAAC,OAAO,EAAE,CAAA;AACvB,YAAA,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAA;YAC/B,MAAM,OAAO,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAA;AACrC,YAAA,IAAI,CAAC,WAAW,GAAG,EAAE,CACpB;YAAA,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC,CAAA;AAC1D,SAAA;KACF;IAEO,MAAM,WAAW,CAAC,MAAc,EAAA;AACtC,QAAA,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG,MAAM,CAAA;QACrC,MAAM,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;QACxC,IAAI,CAAC,GAAG,EAAE;;;;YAIR,OAAM;AACP,SAAA;AAED,QAAA,IAAI,aAA0C,CAAA;AAC9C,QAAA,MAAM,YAAY,GAAG,IAAI,KAAK,YAAY,CAAA;;QAG1C,MAAM,kBAAkB,GAAG,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,EAAE,KACvD,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAC5B,CAAA;AAED,QAAA,IAAI,YAAY,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE;YACjD,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,YAAY,CAAC,CAAA;AAClD,YAAA,IAAI,QAAQ;gBAAE,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAA;YAC5D,IAAI;gBACF,aAAa,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAA;AACvD,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;AACV,gBAAA,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAE,YAAY,CAAC,CAAA;AACvC,aAAA;AACF,SAAA;AAED,QAAA,OAAO,MAAK;YACV,KAAK,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,kBAAkB,EAAE;gBAC7C,EAAE,CACA,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,GAAG,KAAK,YAAY,GAAG,aAAa,GAAG,SAAS,CAAC,CAAC,CACtE,CAAA;AACF,aAAA;AACD,YAAA,MAAM,UAAU,GAAG,YAAY,GAAG,IAAI,GAAG,CAAG,EAAA,YAAY,CAAQ,KAAA,EAAA,IAAI,EAAE,CAAA;YACtE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAuB,oBAAA,EAAA,UAAU,CAAE,CAAA,CAAC,CAAA;AACxD,SAAC,CAAA;KACF;AACF;;ACxTD,MAAM,aAAa,GAAG,mBAAmB,CAAA;AACzC,MAAMA,MAAI,GAAG,QAAQ,IAAI,GAAG,CAAA;AAE5B;AACA,SAAS,CAAC,CACR,CAAS,EACT,QAAgC,EAAE,EAClC,GAAG,QAA2B,EAAA;IAE9B,MAAM,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,CAAA;AACtC,IAAA,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AAC1C,QAAA,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;AACxB,KAAA;AACD,IAAA,IAAI,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAA;AACxB,IAAA,OAAO,IAAI,CAAA;AACb,CAAC;AAED;AACA,MAAM,aAAa,WAAW,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA4I7B,CAAA;AAED;AACA,MAAM,QAAQ,GAAG,CAAC,CAChB,KAAK,EACL,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,UAAU,EAAE,EACvC,CAAC,CACC,KAAK,EACL,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,EACnC,CAAC,CACC,KAAK,EACL,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE,EACrC,CAAC,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,EAC9C,CAAC,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,IAAI,EAAE,cAAc,EAAE,CAAC,CAC3D,EACD,CAAC,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,EACzC,CAAC,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,EAC3C,CAAC,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,EAC3C,CAAC,CACC,KAAK,EACL,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,EAC7B,uBAAuB,EACvB,CAAC,CAAC,KAAK,EAAE,EAAE,EAAE,KAAK,CAAC,EACnB,mCAAmC,EACnC,CAAC,CAAC,IAAI,CAAC,EACP,+CAA+C,EAC/C,CAAC,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,oBAAoB,EAAE,EAAE,oBAAoB,CAAC,EAC/D,MAAM,EACN,CAAC,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,qBAAqB,EAAE,EAAE,OAAO,CAAC,EACnD,MAAM,EACN,CAAC,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,kBAAkB,EAAE,EAAE,aAAa,CAAC,EACtD,GAAG,CACJ,CACF,EACD,CAAC,CAAC,OAAO,EAAE,EAAE,EAAE,aAAa,CAAC,CAC9B,CAAA;AAED,MAAM,MAAM,GAAG,gCAAgC,CAAA;AAC/C,MAAM,WAAW,GAAG,0CAA0C,CAAA;AAE9D;AACA;AACA,MAAM,EAAE,WAAW,GAAG,MAAA;CAAyC,EAAE,GAAG,UAAU,CAAA;AACxE,MAAO,YAAa,SAAQ,WAAW,CAAA;AAI3C,IAAA,WAAA,CAAY,GAAwB,EAAE,KAAK,GAAG,IAAI,EAAA;;AAChD,QAAA,KAAK,EAAE,CAAA;AACP,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAA;AAE/C,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAA;AAE/B,QAAA,WAAW,CAAC,SAAS,GAAG,CAAC,CAAA;AACzB,QAAA,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,IAAI,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;QACzD,MAAM,OAAO,GAAG,QAAQ;cACpB,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;AACtC,cAAE,GAAG,CAAC,OAAO,CAAA;QACf,IAAI,GAAG,CAAC,MAAM,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAW,QAAA,EAAA,GAAG,CAAC,MAAM,CAAI,EAAA,CAAA,CAAC,CAAA;AAChD,SAAA;QACD,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC,CAAA;QAE1C,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,CAAA,EAAA,GAAA,GAAG,CAAC,GAAG,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAI,KAAI,GAAG,CAAC,EAAE,IAAI,cAAc,EAAE,KAAK,CAAC,CAAG,CAAA,CAAA,CAAC,CAAA;QACrE,IAAI,GAAG,CAAC,GAAG,EAAE;YACX,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAG,EAAA,IAAI,CAAI,CAAA,EAAA,GAAG,CAAC,GAAG,CAAC,IAAI,CAAA,CAAA,EAAI,GAAG,CAAC,GAAG,CAAC,MAAM,CAAE,CAAA,EAAE,KAAK,CAAC,CAAA;AACvE,SAAA;aAAM,IAAI,GAAG,CAAC,EAAE,EAAE;AACjB,YAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;AACzB,SAAA;AAED,QAAA,IAAI,QAAQ,EAAE;AACZ,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,KAAM,CAAC,IAAI,EAAE,CAAC,CAAA;AACvC,SAAA;QACD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;AAErC,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAE,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,KAAI;YAClE,CAAC,CAAC,eAAe,EAAE,CAAA;AACrB,SAAC,CAAC,CAAA;AAEF,QAAA,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAK;YAClC,IAAI,CAAC,KAAK,EAAE,CAAA;AACd,SAAC,CAAC,CAAA;AAEF,QAAA,IAAI,CAAC,UAAU,GAAG,CAAC,CAAgB,KAAI;YACrC,IAAI,CAAC,CAAC,GAAG,KAAK,QAAQ,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,EAAE;gBAC7C,IAAI,CAAC,KAAK,EAAE,CAAA;AACb,aAAA;AACH,SAAC,CAAA;QAED,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,CAAA;KACtD;AAED,IAAA,IAAI,CAAC,QAAgB,EAAE,IAAY,EAAE,SAAS,GAAG,KAAK,EAAA;QACpD,MAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAE,CAAA;QAC7C,IAAI,CAAC,SAAS,EAAE;AACd,YAAA,EAAE,CAAC,WAAW,GAAG,IAAI,CAAA;AACtB,SAAA;AAAM,aAAA;YACL,IAAI,QAAQ,GAAG,CAAC,CAAA;AAChB,YAAA,IAAI,KAA6B,CAAA;AACjC,YAAA,MAAM,CAAC,SAAS,GAAG,CAAC,CAAA;YACpB,QAAQ,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;gBAClC,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,KAAK,CAAA;gBAChC,IAAI,KAAK,IAAI,IAAI,EAAE;oBACjB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAA;oBACxC,EAAE,CAAC,WAAW,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAA;oBAC7C,MAAM,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAA;AACxC,oBAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA;AACvB,oBAAA,IAAI,CAAC,SAAS,GAAG,WAAW,CAAA;AAC5B,oBAAA,IAAI,CAAC,OAAO,GAAG,MAAK;wBAClB,KAAK,CACH,IAAI,GAAG,CACL,GAAGA,MAAI,CAAA,sBAAA,EAAyB,kBAAkB,CAAC,IAAI,CAAC,CAAE,CAAA,EAC1D,MAAM,CAAC,IAAI,CAAC,GAAG,CAChB,CACF,CAAA;AACH,qBAAC,CAAA;AACD,oBAAA,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;oBACpB,QAAQ,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;AACtC,iBAAA;AACF,aAAA;AACF,SAAA;KACF;IACD,KAAK,GAAA;;QACH,CAAA,EAAA,GAAA,IAAI,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,WAAW,CAAC,IAAI,CAAC,CAAA;QAClC,QAAQ,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,CAAA;KACzD;AACF,CAAA;AAEM,MAAM,SAAS,GAAG,oBAAoB,CAAA;AAC7C,MAAM,EAAE,cAAc,EAAE,GAAG,UAAU,CAAA;AACrC,IAAI,cAAc,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;AACpD,IAAA,cAAc,CAAC,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,CAAA;AAC/C;;;ACtRD,OAAO,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAA;AAErC,MAAM,aAAa,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;AAE9C;AACA,MAAM,UAAU,GAAG,eAAe,CAAA;AAClC,MAAM,cAAc,GAClB,gBAAgB,KAAK,aAAa,CAAC,QAAQ,KAAK,QAAQ,GAAG,KAAK,GAAG,IAAI,CAAC,CAAA;AAC1E,MAAM,OAAO,GAAG,YAAY,CAAA;AAC5B,MAAM,UAAU,GAAG,CAAA,EAAG,gBAAgB,IAAI,aAAa,CAAC,QAAQ,CAC9D,CAAA,EAAA,OAAO,IAAI,aAAa,CAAC,IAC3B,CAAG,EAAA,YAAY,EAAE,CAAA;AACjB,MAAM,gBAAgB,GAAG,qBAAqB,CAAA;AAC9C,MAAM,IAAI,GAAG,QAAQ,IAAI,GAAG,CAAA;AAE5B,IAAI,MAAiB,CAAA;AACrB,IAAI;AACF,IAAA,IAAI,QAAkC,CAAA;;IAEtC,IAAI,CAAC,OAAO,EAAE;QACZ,QAAQ,GAAG,MAAK;;;YAGd,MAAM,GAAG,cAAc,CAAC,cAAc,EAAE,gBAAgB,EAAE,MAAK;gBAC7D,MAAM,oBAAoB,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;AACrD,gBAAA,MAAM,iBAAiB,GACrB,oBAAoB,CAAC,IAAI;oBACzB,oBAAoB,CAAC,QAAQ,CAAC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAA;gBAC7D,OAAO,CAAC,KAAK,CACX,0CAA0C;oBACxC,uBAAuB;oBACvB,CAAe,YAAA,EAAA,iBAAiB,CAAiB,cAAA,EAAA,UAAU,CAAa,WAAA,CAAA;oBACxE,CAAe,YAAA,EAAA,UAAU,CAAgC,6BAAA,EAAA,gBAAgB,CAAa,WAAA,CAAA;AACtF,oBAAA,4GAA4G,CAC/G,CAAA;AACH,aAAC,CAAC,CAAA;AACF,YAAA,MAAM,CAAC,gBAAgB,CACrB,MAAM,EACN,MAAK;AACH,gBAAA,OAAO,CAAC,IAAI,CACV,0JAA0J,CAC3J,CAAA;AACH,aAAC,EACD,EAAE,IAAI,EAAE,IAAI,EAAE,CACf,CAAA;AACH,SAAC,CAAA;AACF,KAAA;IAED,MAAM,GAAG,cAAc,CAAC,cAAc,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAA;AAC9D,CAAA;AAAC,OAAO,KAAK,EAAE;AACd,IAAA,OAAO,CAAC,KAAK,CAAC,0CAA0C,KAAK,CAAA,GAAA,CAAK,CAAC,CAAA;AACpE,CAAA;AAED,SAAS,cAAc,CACrB,QAAgB,EAChB,WAAmB,EACnB,kBAA+B,EAAA;AAE/B,IAAA,MAAM,MAAM,GAAG,IAAI,SAAS,CAAC,CAAA,EAAG,QAAQ,CAAA,GAAA,EAAM,WAAW,CAAA,CAAE,EAAE,UAAU,CAAC,CAAA;IACxE,IAAI,QAAQ,GAAG,KAAK,CAAA;AAEpB,IAAA,MAAM,CAAC,gBAAgB,CACrB,MAAM,EACN,MAAK;QACH,QAAQ,GAAG,IAAI,CAAA;QACf,eAAe,CAAC,iBAAiB,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,CAAA;AAC3D,KAAC,EACD,EAAE,IAAI,EAAE,IAAI,EAAE,CACf,CAAA;;IAGD,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,KAAI;QACpD,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAA;AACjC,KAAC,CAAC,CAAA;;IAGF,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAI;AACtD,QAAA,IAAI,QAAQ;YAAE,OAAM;AAEpB,QAAA,IAAI,CAAC,QAAQ,IAAI,kBAAkB,EAAE;AACnC,YAAA,kBAAkB,EAAE,CAAA;YACpB,OAAM;AACP,SAAA;QAED,eAAe,CAAC,oBAAoB,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,CAAA;AAE5D,QAAA,OAAO,CAAC,GAAG,CAAC,CAAA,qDAAA,CAAuD,CAAC,CAAA;AACpE,QAAA,MAAM,qBAAqB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAA;QAClD,QAAQ,CAAC,MAAM,EAAE,CAAA;AACnB,KAAC,CAAC,CAAA;AAEF,IAAA,OAAO,MAAM,CAAA;AACf,CAAC;AAED,SAAS,QAAQ,CAAC,QAAgB,EAAA;IAChC,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,QAAQ,EAAE,mBAAmB,CAAC,CAAA;AAClD,IAAA,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;AACjC,IAAA,OAAO,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAA;AAClC,CAAC;AAED,IAAI,aAAa,GAAG,IAAI,CAAA;AACxB,MAAM,gBAAgB,GAAG,IAAI,OAAO,EAAmB,CAAA;AAEvD,MAAM,cAAc,GAAG,CAAC,IAAY,KAAI;AACtC,IAAA,IAAI,KAA2C,CAAA;AAC/C,IAAA,OAAO,MAAK;AACV,QAAA,IAAI,KAAK,EAAE;YACT,YAAY,CAAC,KAAK,CAAC,CAAA;YACnB,KAAK,GAAG,IAAI,CAAA;AACb,SAAA;AACD,QAAA,KAAK,GAAG,UAAU,CAAC,MAAK;YACtB,QAAQ,CAAC,MAAM,EAAE,CAAA;SAClB,EAAE,IAAI,CAAC,CAAA;AACV,KAAC,CAAA;AACH,CAAC,CAAA;AACD,MAAM,UAAU,GAAG,cAAc,CAAC,EAAE,CAAC,CAAA;AAErC,MAAM,SAAS,GAAG,IAAI,SAAS,CAC7B,OAAO,EACP;IACE,OAAO,EAAE,MAAM,MAAM,IAAI,MAAM,CAAC,UAAU,KAAK,CAAC;IAChD,IAAI,EAAE,CAAC,OAAO,KAAK,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;AACxC,CAAA,EACD,eAAe,mBAAmB,CAAC,EACjC,YAAY,EACZ,SAAS,EACT,sBAAsB,EACtB,sBAAsB,GACvB,EAAA;AACC,IAAA,MAAM,CAAC,wBAAwB,EAAE,KAAK,CAAC,GAAG,YAAY,CAAC,KAAK,CAAC,CAAG,CAAA,CAAA,CAAC,CAAA;IACjE,MAAM,aAAa,GAAG;;IAEpB,IAAI;AACF,QAAA,wBAAwB,CAAC,KAAK,CAAC,CAAC,CAAC;QACjC,CAAI,CAAA,EAAA,sBAAsB,GAAG,SAAS,GAAG,EAAE,CAAA,EAAA,EAAK,SAAS,CAAA,EACvD,KAAK,GAAG,CAAA,CAAA,EAAI,KAAK,CAAA,CAAE,GAAG,EACxB,CAAE,CAAA,CACL,CAAA;AACD,IAAA,IAAI,sBAAsB,EAAE;AAC1B,QAAA,aAAa,CAAC,KAAK,CAAC,MAAK;AACvB,YAAA,OAAO,CAAC,IAAI,CACV,CAAA,MAAA,EAAS,YAAY,CAAsG,oGAAA,CAAA;AACzH,gBAAA,CAAA,2IAAA,CAA6I,CAChJ,CAAA;AACD,YAAA,UAAU,EAAE,CAAA;AACd,SAAC,CAAC,CAAA;AACH,KAAA;IACD,OAAO,MAAM,aAAa,CAAA;AAC5B,CAAC,CACF,CAAA;AAED,eAAe,aAAa,CAAC,OAAmB,EAAA;IAC9C,QAAQ,OAAO,CAAC,IAAI;AAClB,QAAA,KAAK,WAAW;AACd,YAAA,OAAO,CAAC,KAAK,CAAC,CAAA,iBAAA,CAAmB,CAAC,CAAA;AAClC,YAAA,SAAS,CAAC,SAAS,CAAC,KAAK,EAAE,CAAA;;;YAG3B,WAAW,CAAC,MAAK;AACf,gBAAA,IAAI,MAAM,CAAC,UAAU,KAAK,MAAM,CAAC,IAAI,EAAE;AACrC,oBAAA,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;AAC/B,iBAAA;aACF,EAAE,eAAe,CAAC,CAAA;YACnB,MAAK;AACP,QAAA,KAAK,QAAQ;AACX,YAAA,eAAe,CAAC,mBAAmB,EAAE,OAAO,CAAC,CAAA;;;;;AAK7C,YAAA,IAAI,aAAa,IAAI,eAAe,EAAE,EAAE;AACtC,gBAAA,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAA;gBACxB,OAAM;AACP,aAAA;AAAM,iBAAA;AACL,gBAAA,iBAAiB,EAAE,CAAA;gBACnB,aAAa,GAAG,KAAK,CAAA;AACtB,aAAA;AACD,YAAA,MAAM,OAAO,CAAC,GAAG,CACf,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,MAAM,KAAmB;AAClD,gBAAA,IAAI,MAAM,CAAC,IAAI,KAAK,WAAW,EAAE;AAC/B,oBAAA,OAAO,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;AACrC,iBAAA;;;AAID,gBAAA,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,MAAM,CAAA;AAClC,gBAAA,MAAM,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAA;;;;AAIhC,gBAAA,MAAM,EAAE,GAAG,KAAK,CAAC,IAAI,CACnB,QAAQ,CAAC,gBAAgB,CAAkB,MAAM,CAAC,CACnD,CAAC,IAAI,CACJ,CAAC,CAAC,KACA,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CACnE,CAAA;gBAED,IAAI,CAAC,EAAE,EAAE;oBACP,OAAM;AACP,iBAAA;AAED,gBAAA,MAAM,OAAO,GAAG,CAAG,EAAA,IAAI,CAAG,EAAA,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA,EAC1C,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAClC,CAAK,EAAA,EAAA,SAAS,EAAE,CAAA;;;;;;AAOhB,gBAAA,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,KAAI;AAC7B,oBAAA,MAAM,UAAU,GAAG,EAAE,CAAC,SAAS,EAAqB,CAAA;AACpD,oBAAA,UAAU,CAAC,IAAI,GAAG,IAAI,GAAG,CAAC,OAAO,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CAAA;oBAChD,MAAM,WAAW,GAAG,MAAK;wBACvB,EAAE,CAAC,MAAM,EAAE,CAAA;AACX,wBAAA,OAAO,CAAC,KAAK,CAAC,2BAA2B,SAAS,CAAA,CAAE,CAAC,CAAA;AACrD,wBAAA,OAAO,EAAE,CAAA;AACX,qBAAC,CAAA;AACD,oBAAA,UAAU,CAAC,gBAAgB,CAAC,MAAM,EAAE,WAAW,CAAC,CAAA;AAChD,oBAAA,UAAU,CAAC,gBAAgB,CAAC,OAAO,EAAE,WAAW,CAAC,CAAA;AACjD,oBAAA,gBAAgB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;AACxB,oBAAA,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,CAAA;AACtB,iBAAC,CAAC,CAAA;aACH,CAAC,CACH,CAAA;AACD,YAAA,eAAe,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAA;YAC5C,MAAK;QACP,KAAK,QAAQ,EAAE;YACb,eAAe,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,CAAA;YAC5C,MAAK;AACN,SAAA;AACD,QAAA,KAAK,aAAa;AAChB,YAAA,eAAe,CAAC,uBAAuB,EAAE,OAAO,CAAC,CAAA;AACjD,YAAA,IAAI,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;;;gBAGlD,MAAM,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;AAC7C,gBAAA,MAAM,WAAW,GAAG,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;gBAChD,IACE,QAAQ,KAAK,WAAW;oBACxB,OAAO,CAAC,IAAI,KAAK,aAAa;AAC9B,qBAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,QAAQ,GAAG,YAAY,KAAK,WAAW,CAAC,EACnE;AACA,oBAAA,UAAU,EAAE,CAAA;AACb,iBAAA;gBACD,OAAM;AACP,aAAA;AAAM,iBAAA;AACL,gBAAA,UAAU,EAAE,CAAA;AACb,aAAA;YACD,MAAK;AACP,QAAA,KAAK,OAAO;AACV,YAAA,eAAe,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAA;YAC5C,MAAM,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;YACzC,MAAK;QACP,KAAK,OAAO,EAAE;AACZ,YAAA,eAAe,CAAC,YAAY,EAAE,OAAO,CAAC,CAAA;AACtC,YAAA,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAA;AACvB,YAAA,IAAI,aAAa,EAAE;gBACjB,kBAAkB,CAAC,GAAG,CAAC,CAAA;AACxB,aAAA;AAAM,iBAAA;AACL,gBAAA,OAAO,CAAC,KAAK,CACX,CAAA,8BAAA,EAAiC,GAAG,CAAC,OAAO,CAAA,EAAA,EAAK,GAAG,CAAC,KAAK,CAAA,CAAE,CAC7D,CAAA;AACF,aAAA;YACD,MAAK;AACN,SAAA;AACD,QAAA,SAAS;YACP,MAAM,KAAK,GAAU,OAAO,CAAA;AAC5B,YAAA,OAAO,KAAK,CAAA;AACb,SAAA;AACF,KAAA;AACH,CAAC;AAMD,SAAS,eAAe,CAAC,KAAa,EAAE,IAAS,EAAA;AAC/C,IAAA,SAAS,CAAC,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;AACxC,CAAC;AAED,MAAM,aAAa,GAAG,sBAAsB,CAAA;AAE5C,SAAS,kBAAkB,CAAC,GAAwB,EAAA;AAClD,IAAA,iBAAiB,EAAE,CAAA;IACnB,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,YAAY,CAAC,GAAG,CAAC,CAAC,CAAA;AAClD,CAAC;AAED,SAAS,iBAAiB,GAAA;AACxB,IAAA,QAAQ,CAAC,gBAAgB,CAAe,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC,CAAA;AAC9E,CAAC;AAED,SAAS,eAAe,GAAA;IACtB,OAAO,QAAQ,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,MAAM,CAAA;AACpD,CAAC;AAED,eAAe,qBAAqB,CAClC,cAAsB,EACtB,WAAmB,EACnB,EAAE,GAAG,IAAI,EAAA;AAET,IAAA,MAAM,gBAAgB,GAAG,cAAc,KAAK,KAAK,GAAG,OAAO,GAAG,MAAM,CAAA;AAEpE,IAAA,MAAM,IAAI,GAAG,YAAW;;;;QAItB,IAAI;AACF,YAAA,MAAM,KAAK,CAAC,CAAA,EAAG,gBAAgB,CAAM,GAAA,EAAA,WAAW,EAAE,EAAE;AAClD,gBAAA,IAAI,EAAE,SAAS;AACf,gBAAA,OAAO,EAAE;;;AAGP,oBAAA,MAAM,EAAE,kBAAkB;AAC3B,iBAAA;AACF,aAAA,CAAC,CAAA;AACF,YAAA,OAAO,IAAI,CAAA;AACZ,SAAA;AAAC,QAAA,MAAM,GAAE;AACV,QAAA,OAAO,KAAK,CAAA;AACd,KAAC,CAAA;IAED,IAAI,MAAM,IAAI,EAAE,EAAE;QAChB,OAAM;AACP,KAAA;AACD,IAAA,MAAM,IAAI,CAAC,EAAE,CAAC,CAAA;;AAGd,IAAA,OAAO,IAAI,EAAE;AACX,QAAA,IAAI,QAAQ,CAAC,eAAe,KAAK,SAAS,EAAE;YAC1C,IAAI,MAAM,IAAI,EAAE,EAAE;gBAChB,MAAK;AACN,aAAA;AACD,YAAA,MAAM,IAAI,CAAC,EAAE,CAAC,CAAA;AACf,SAAA;AAAM,aAAA;YACL,MAAM,iBAAiB,EAAE,CAAA;AAC1B,SAAA;AACF,KAAA;AACH,CAAC;AAED,SAAS,IAAI,CAAC,EAAU,EAAA;AACtB,IAAA,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,KAAK,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAA;AAC1D,CAAC;AAED,SAAS,iBAAiB,GAAA;AACxB,IAAA,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,KAAI;AACnC,QAAA,MAAM,QAAQ,GAAG,YAAW;AAC1B,YAAA,IAAI,QAAQ,CAAC,eAAe,KAAK,SAAS,EAAE;AAC1C,gBAAA,OAAO,EAAE,CAAA;AACT,gBAAA,QAAQ,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAA;AAC3D,aAAA;AACH,SAAC,CAAA;AACD,QAAA,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAA;AACzD,KAAC,CAAC,CAAA;AACJ,CAAC;AAED,MAAM,SAAS,GAAG,IAAI,GAAG,EAA4B,CAAA;AAErD;AACA;AACA,IAAI,UAAU,IAAI,UAAU,EAAE;IAC5B,QAAQ;SACL,gBAAgB,CAAmB,yBAAyB,CAAC;AAC7D,SAAA,OAAO,CAAC,CAAC,EAAE,KAAI;AACd,QAAA,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,YAAY,CAAC,kBAAkB,CAAE,EAAE,EAAE,CAAC,CAAA;AACzD,KAAC,CAAC,CAAA;AACL,CAAA;AAED,MAAM,QAAQ,GACZ,UAAU,IAAI,UAAU;MACpB,MAAA,QAAQ,CAAC,aAAa,CAAkB,0BAA0B,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,KAAK;MAC1E,SAAS,CAAA;AAEf;AACA;AACA,IAAI,iBAA+C,CAAA;AAEnC,SAAA,WAAW,CAAC,EAAU,EAAE,OAAe,EAAA;IACrD,IAAI,KAAK,GAAG,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;IAC7B,IAAI,CAAC,KAAK,EAAE;AACV,QAAA,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAA;AACvC,QAAA,KAAK,CAAC,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC,CAAA;AACtC,QAAA,KAAK,CAAC,YAAY,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAA;AAC1C,QAAA,KAAK,CAAC,WAAW,GAAG,OAAO,CAAA;AAC3B,QAAA,IAAI,QAAQ,EAAE;AACZ,YAAA,KAAK,CAAC,YAAY,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAA;AACtC,SAAA;QAED,IAAI,CAAC,iBAAiB,EAAE;AACtB,YAAA,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;;;YAIhC,UAAU,CAAC,MAAK;gBACd,iBAAiB,GAAG,SAAS,CAAA;aAC9B,EAAE,CAAC,CAAC,CAAA;AACN,SAAA;AAAM,aAAA;AACL,YAAA,iBAAiB,CAAC,qBAAqB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAA;AAC3D,SAAA;QACD,iBAAiB,GAAG,KAAK,CAAA;AAC1B,KAAA;AAAM,SAAA;AACL,QAAA,KAAK,CAAC,WAAW,GAAG,OAAO,CAAA;AAC5B,KAAA;AACD,IAAA,SAAS,CAAC,GAAG,CAAC,EAAE,EAAE,KAAK,CAAC,CAAA;AAC1B,CAAC;AAEK,SAAU,WAAW,CAAC,EAAU,EAAA;IACpC,MAAM,KAAK,GAAG,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;AAC/B,IAAA,IAAI,KAAK,EAAE;AACT,QAAA,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;AAChC,QAAA,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA;AACrB,KAAA;AACH,CAAC;AAEK,SAAU,gBAAgB,CAAC,SAAiB,EAAA;AAChD,IAAA,OAAO,IAAI,UAAU,CAAC,SAAS,EAAE,SAAS,CAAC,CAAA;AAC7C,CAAC;AAED;;AAEG;AACa,SAAA,WAAW,CAAC,GAAW,EAAE,aAAqB,EAAA;;AAE5D,IAAA,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;AACpC,QAAA,OAAO,GAAG,CAAA;AACX,KAAA;;IAGD,MAAM,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAA;AAC3C,IAAA,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,GAAG,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAAA;IAE1D,OAAO,CAAA,EAAG,QAAQ,CAAA,CAAA,EAAI,aAAa,CAAA,EAAG,MAAM,GAAG,CAAG,CAAA,CAAA,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,CAAA,EACvE,IAAI,IAAI,EACV,CAAA,CAAE,CAAA;AACJ;;;;", "x_google_ignoreList": [0, 1, 2]}