/* eslint-disable no-restricted-globals */

warnCjsUsage()

// type utils
module.exports.defineConfig = (config) => config

// proxy cjs utils (sync functions)
Object.assign(module.exports, require('./dist/node-cjs/publicUtils.cjs'))

// async functions, can be redirect from ESM build
const asyncFunctions = [
  'build',
  'createServer',
  'preview',
  'transformWithEsbuild',
  'resolveConfig',
  'optimizeDeps',
  'formatPostcssSourceMap',
  'loadConfigFromFile',
  'preprocessCSS',
]
asyncFunctions.forEach((name) => {
  module.exports[name] = (...args) =>
    import('./dist/node/index.js').then((i) => i[name](...args))
})

function warnCjsUsage() {
  if (process.env.VITE_CJS_IGNORE_WARNING) return
  const yellow = (str) => `\u001b[33m${str}\u001b[39m`
  const log = process.env.VITE_CJS_TRACE ? console.trace : console.warn
  log(
    yellow(
      `The CJS build of Vite's Node API is deprecated. See https://vitejs.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.`,
    ),
  )
}
