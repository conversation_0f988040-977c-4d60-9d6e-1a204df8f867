#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/tsup@8.0.2_typescript@5.4.3/node_modules/tsup/dist/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/tsup@8.0.2_typescript@5.4.3/node_modules/tsup/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/tsup@8.0.2_typescript@5.4.3/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/tsup@8.0.2_typescript@5.4.3/node_modules/tsup/dist/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/tsup@8.0.2_typescript@5.4.3/node_modules/tsup/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/tsup@8.0.2_typescript@5.4.3/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../node_modules/.pnpm/tsup@8.0.2_typescript@5.4.3/node_modules/tsup/dist/cli-node.js" "$@"
else
  exec node  "$basedir/../../../../node_modules/.pnpm/tsup@8.0.2_typescript@5.4.3/node_modules/tsup/dist/cli-node.js" "$@"
fi
