{"name": "@workflow-mapper/kg-cli", "version": "0.0.1", "description": "Knowledge graph CLI tool for building graphs from MDX specifications", "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "bin": {"build-kg": "./dist/build-kg.js"}, "scripts": {"build": "tsup src/index.ts src/build-kg.ts --format esm --dts", "type-check": "tsc --noEmit", "test": "jest", "test:coverage": "jest --coverage"}, "keywords": ["knowledge-graph", "cli", "mdx", "json-ld", "yaml"], "author": "WorkflowMapper Team", "license": "ISC"}