import { ParseError } from '@workflow-mapper/spec-parser-lib';
export { ParseError, ParsedSpec } from '@workflow-mapper/spec-parser-lib';

interface KnowledgeGraphOptions {
    dryRun?: boolean;
    outputDir?: string;
}
interface KnowledgeGraphResult {
    summary: {
        specsCount: number;
        milestonesCount: number;
        componentsCount: number;
        relationshipsCount: number;
    };
    files: {
        jsonld?: string;
        yaml?: string;
    };
    errors: ParseError[];
}
interface GraphNode {
    '@id': string;
    '@type': string;
    title?: string;
    description?: string;
    status?: string;
    version?: string;
    created?: string;
    updated?: string;
    tags?: string[];
    authors?: string[];
    filePath: string;
}
interface GraphRelationship {
    '@type': string;
    source: string;
    target: string;
    relationship: string;
}
interface KnowledgeGraph {
    '@context': {
        '@vocab': string;
        title: string;
        description: string;
        status: string;
        version: string;
        created: string;
        updated: string;
        tags: string;
        authors: string;
        filePath: string;
        implements: string;
        dependsOn: string;
        contains: string;
    };
    '@graph': (GraphNode | GraphRelationship)[];
}
/**
 * Build a knowledge graph from MDX specifications
 */
declare function buildKnowledgeGraph(directory: string, options?: KnowledgeGraphOptions): Promise<KnowledgeGraphResult>;

export { type GraphNode, type GraphRelationship, type KnowledgeGraph, type KnowledgeGraphOptions, type KnowledgeGraphResult, buildKnowledgeGraph };
