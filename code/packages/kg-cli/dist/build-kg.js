#!/usr/bin/env node
import {
  buildKnowledgeGraph
} from "./chunk-VM2H3APQ.js";

// src/build-kg.ts
import { parseArgs } from "util";
function showHelp() {
  console.log(`
Usage: build-kg [options] <directory>

Build a knowledge graph from MDX specification files.

Arguments:
  directory         Directory containing MDX files to parse

Options:
  --dry-run        Print summary without writing files
  --help           Show this help message

Examples:
  build-kg docs/tech-specs
  build-kg --dry-run docs/tech-specs
`);
}
async function main() {
  try {
    const args = process.argv.slice(2).filter((arg) => arg !== "--");
    const { values, positionals } = parseArgs({
      args,
      options: {
        "dry-run": {
          type: "boolean",
          default: false
        },
        help: {
          type: "boolean",
          default: false
        }
      },
      allowPositionals: true
    });
    if (values.help) {
      showHelp();
      process.exit(0);
    }
    const directory = positionals[0];
    if (!directory) {
      console.error("Error: Directory argument is required");
      showHelp();
      process.exit(1);
    }
    const isDryRun = values["dry-run"] || false;
    console.log(`\u{1F517} Building knowledge graph from: ${directory}`);
    if (isDryRun) {
      console.log("\u{1F4CB} Dry run mode - no files will be written");
    }
    const result = await buildKnowledgeGraph(directory, { dryRun: isDryRun });
    if (isDryRun) {
      console.log("\n\u{1F4CA} Summary:");
      console.log(`  Specs found: ${result.summary.specsCount}`);
      console.log(`  Milestones: ${result.summary.milestonesCount}`);
      console.log(`  Components: ${result.summary.componentsCount}`);
      console.log(`  Relationships: ${result.summary.relationshipsCount}`);
      if (result.errors.length > 0) {
        console.log(`
\u26A0\uFE0F  Errors: ${result.errors.length}`);
        result.errors.forEach((error) => {
          console.log(`  - ${error.filePath}: ${error.error}`);
        });
      }
    } else {
      console.log(`
\u2705 Knowledge graph built successfully!`);
      console.log(`  \u{1F4C4} JSON-LD: ${result.files.jsonld}`);
      console.log(`  \u{1F4C4} YAML: ${result.files.yaml}`);
      console.log(`  \u{1F4CA} Specs processed: ${result.summary.specsCount}`);
      if (result.errors.length > 0) {
        console.log(`
\u26A0\uFE0F  Warnings: ${result.errors.length}`);
        result.errors.forEach((error) => {
          console.log(`  - ${error.filePath}: ${error.error}`);
        });
      }
    }
    if (result.errors.length > 0) {
      process.exit(1);
    }
  } catch (error) {
    console.error("\u274C Error:", error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
}
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
