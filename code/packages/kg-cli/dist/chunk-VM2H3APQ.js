// src/index.ts
import { parseSpecsDirectory } from "@workflow-mapper/spec-parser-lib";
import { writeFileSync } from "fs";
import { join } from "path";
import { stringify as yamlStringify } from "yaml";
async function buildKnowledgeGraph(directory, options = {}) {
  const { dryRun = false, outputDir = "." } = options;
  const parseResult = parseSpecsDirectory(directory);
  const graph = buildGraph(parseResult.specs);
  const jsonldContent = JSON.stringify(graph, null, 2);
  const yamlContent = yamlStringify(graph, { indent: 2 });
  const files = {};
  if (!dryRun) {
    const jsonldPath = join(outputDir, "kg.jsonld");
    const yamlPath = join(outputDir, "kg.yaml");
    writeFileSync(jsonldPath, jsonldContent, "utf-8");
    writeFileSync(yamlPath, yamlContent, "utf-8");
    files.jsonld = jsonldPath;
    files.yaml = yamlPath;
  }
  const nodes = graph["@graph"].filter((item) => item["@type"] !== "Relationship");
  const relationships = graph["@graph"].filter((item) => item["@type"] === "Relationship");
  const milestonesCount = nodes.filter(
    (node) => node["@type"] === "Milestone" || node.filePath.includes("/milestones/") || node.tags?.includes("milestone")
  ).length;
  const componentsCount = nodes.filter(
    (node) => node["@type"] === "Component" || node.filePath.includes("/components/") || node.tags?.includes("component")
  ).length;
  return {
    summary: {
      specsCount: parseResult.specs.length,
      milestonesCount,
      componentsCount,
      relationshipsCount: relationships.length
    },
    files,
    errors: parseResult.errors
  };
}
function buildGraph(specs) {
  const nodes = [];
  const relationships = [];
  for (const spec of specs) {
    const node = {
      "@id": spec.id,
      "@type": determineNodeType(spec),
      filePath: spec.filePath,
      ...spec.frontmatter
    };
    nodes.push(node);
    const specRelationships = extractRelationships(spec);
    relationships.push(...specRelationships);
  }
  return {
    "@context": {
      "@vocab": "https://workflow-mapper.dev/vocab#",
      title: "https://schema.org/name",
      description: "https://schema.org/description",
      status: "https://workflow-mapper.dev/vocab#status",
      version: "https://schema.org/version",
      created: "https://schema.org/dateCreated",
      updated: "https://schema.org/dateModified",
      tags: "https://schema.org/keywords",
      authors: "https://schema.org/author",
      filePath: "https://workflow-mapper.dev/vocab#filePath",
      implements: "https://workflow-mapper.dev/vocab#implements",
      dependsOn: "https://workflow-mapper.dev/vocab#dependsOn",
      contains: "https://workflow-mapper.dev/vocab#contains"
    },
    "@graph": [...nodes, ...relationships]
  };
}
function determineNodeType(spec) {
  if (spec.filePath.includes("/milestones/")) {
    return "Milestone";
  }
  if (spec.filePath.includes("/components/")) {
    return "Component";
  }
  if (spec.filePath.includes("/domains/")) {
    return "Domain";
  }
  if (spec.filePath.includes("/adrs/")) {
    return "ArchitecturalDecision";
  }
  if (spec.frontmatter.tags?.includes("milestone")) {
    return "Milestone";
  }
  if (spec.frontmatter.tags?.includes("component")) {
    return "Component";
  }
  if (spec.frontmatter.tags?.includes("domain")) {
    return "Domain";
  }
  if (spec.frontmatter.tags?.includes("adr")) {
    return "ArchitecturalDecision";
  }
  return "Specification";
}
function extractRelationships(spec) {
  const relationships = [];
  if (spec.frontmatter.implements) {
    const implementsTargets = Array.isArray(spec.frontmatter.implements) ? spec.frontmatter.implements : [spec.frontmatter.implements];
    for (const target of implementsTargets) {
      relationships.push({
        "@type": "Relationship",
        source: spec.id,
        target: String(target),
        relationship: "implements"
      });
    }
  }
  if (spec.frontmatter.dependsOn) {
    const dependsOn = Array.isArray(spec.frontmatter.dependsOn) ? spec.frontmatter.dependsOn : [spec.frontmatter.dependsOn];
    for (const target of dependsOn) {
      relationships.push({
        "@type": "Relationship",
        source: spec.id,
        target: String(target),
        relationship: "dependsOn"
      });
    }
  }
  return relationships;
}

export {
  buildKnowledgeGraph
};
