interface SpecFrontmatter {
    title?: string;
    description?: string;
    created?: string;
    updated?: string;
    version?: string;
    status?: string;
    tags?: string[];
    authors?: string[];
    [key: string]: any;
}
interface ParsedSpec {
    id: string;
    filePath: string;
    frontmatter: SpecFrontmatter;
    content: string;
    headings: Heading[];
}
interface Heading {
    level: number;
    text: string;
    id: string;
}
interface ParseResult {
    specs: ParsedSpec[];
    errors: ParseError[];
}
interface ParseError {
    filePath: string;
    error: string;
    details?: string;
}
/**
 * Parse all MDX files in a directory recursively
 */
declare function parseSpecsDirectory(directoryPath: string): ParseResult;
/**
 * Parse a single MDX file
 */
declare function parseSpecFile(filePath: string): ParsedSpec;

export { type Heading, type ParseError, type ParseResult, type ParsedSpec, type SpecFrontmatter, parseSpecFile, parseSpecsDirectory };
