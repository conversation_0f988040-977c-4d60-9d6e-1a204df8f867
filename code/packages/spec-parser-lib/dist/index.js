// src/parse-specs.ts
import matter from "gray-matter";
import { readFileSync, readdirSync, statSync } from "fs";
import { join, extname } from "path";
function parseSpecsDirectory(directoryPath) {
  const specs = [];
  const errors = [];
  try {
    const files = findMdxFiles(directoryPath);
    for (const filePath of files) {
      try {
        const spec = parseSpecFile(filePath);
        specs.push(spec);
      } catch (error) {
        errors.push({
          filePath,
          error: "Failed to parse file",
          details: error instanceof Error ? error.message : String(error)
        });
      }
    }
  } catch (error) {
    errors.push({
      filePath: directoryPath,
      error: "Failed to read directory",
      details: error instanceof Error ? error.message : String(error)
    });
  }
  return { specs, errors };
}
function parseSpecFile(filePath) {
  const content = readFileSync(filePath, "utf-8");
  const parsed = matter(content);
  const headings = extractHeadings(parsed.content);
  return {
    id: generateSpecId(filePath, parsed.data),
    filePath,
    frontmatter: parsed.data,
    content: parsed.content,
    headings
  };
}
function findMdxFiles(directoryPath) {
  const files = [];
  function traverse(currentPath) {
    const items = readdirSync(currentPath);
    for (const item of items) {
      const fullPath = join(currentPath, item);
      const stat = statSync(fullPath);
      if (stat.isDirectory()) {
        traverse(fullPath);
      } else if (stat.isFile() && extname(item) === ".mdx") {
        files.push(fullPath);
      }
    }
  }
  traverse(directoryPath);
  return files;
}
function extractHeadings(content) {
  const headings = [];
  const lines = content.split("\n");
  for (const line of lines) {
    const match = line.match(/^(#{1,6})\s+(.+)$/);
    if (match) {
      const level = match[1]?.length ?? 0;
      const text = match[2]?.trim() ?? "";
      const id = generateHeadingId(text);
      headings.push({
        level,
        text,
        id
      });
    }
  }
  return headings;
}
function generateSpecId(filePath, frontmatter) {
  const title = frontmatter.title || filePath.split("/").pop()?.replace(".mdx", "") || "unknown";
  const seed = `${filePath}-${title}`;
  return `spec-${seed.replace(/[^a-zA-Z0-9]/g, "-").toLowerCase()}`;
}
function generateHeadingId(text) {
  return text.toLowerCase().replace(/[^a-zA-Z0-9\s]/g, "").replace(/\s+/g, "-").replace(/^-+|-+$/g, "");
}
export {
  parseSpecFile,
  parseSpecsDirectory
};
