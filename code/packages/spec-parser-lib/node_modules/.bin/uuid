#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/uuid@9.0.0/node_modules/uuid/dist/bin/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/uuid@9.0.0/node_modules/uuid/dist/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/uuid@9.0.0/node_modules/uuid/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/uuid@9.0.0/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/uuid@9.0.0/node_modules/uuid/dist/bin/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/uuid@9.0.0/node_modules/uuid/dist/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/uuid@9.0.0/node_modules/uuid/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/uuid@9.0.0/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../uuid/dist/bin/uuid" "$@"
else
  exec node  "$basedir/../uuid/dist/bin/uuid" "$@"
fi
